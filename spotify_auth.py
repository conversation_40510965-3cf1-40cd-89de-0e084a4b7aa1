import base64
import json
import logging
import os
import time
import urllib.parse
import webbrowser
import socket
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import urlparse, parse_qs
import threading
import requests
from config import Config

class CallbackHandler(BaseHTTPRequestHandler):
    """HTTP handler for OAuth callback"""

    def do_GET(self):
        """Handle GET request for OAuth callback"""
        parsed_path = urlparse(self.path)
        query_params = parse_qs(parsed_path.query)

        if 'code' in query_params:
            self.server.auth_code = query_params['code'][0]
            self.send_response(200)
            self.send_header('Content-type', 'text/html')
            self.end_headers()
            self.wfile.write(b'''
            <html>
            <head><title>Spotify Tracker - Authentication Successful</title></head>
            <body style="font-family: Arial, sans-serif; text-align: center; margin-top: 100px;">
                <h1 style="color: #1DB954;">✅ Authentication Successful!</h1>
                <p>You can now close this window and return to your terminal.</p>
                <p>The Spotify Tracker is now authorized to access your account.</p>
            </body>
            </html>
            ''')
        elif 'error' in query_params:
            self.server.auth_error = query_params['error'][0]
            self.send_response(400)
            self.send_header('Content-type', 'text/html')
            self.end_headers()
            self.wfile.write(f'''
            <html>
            <head><title>Spotify Tracker - Authentication Error</title></head>
            <body style="font-family: Arial, sans-serif; text-align: center; margin-top: 100px;">
                <h1 style="color: #ff0000;">❌ Authentication Error</h1>
                <p>Error: {query_params['error'][0]}</p>
                <p>Please try again or check your configuration.</p>
            </body>
            </html>
            '''.encode())
        else:
            self.send_response(400)
            self.send_header('Content-type', 'text/html')
            self.end_headers()
            self.wfile.write(b'Invalid callback')

    def log_message(self, format, *args):
        """Suppress default HTTP server logging"""
        pass

class SpotifyAuth:
    def __init__(self):
        self.client_id = Config.SPOTIFY_CLIENT_ID
        self.client_secret = Config.SPOTIFY_CLIENT_SECRET
        self.redirect_uri = Config.SPOTIFY_REDIRECT_URI
        self.scopes = ' '.join(Config.SPOTIFY_SCOPES)
        self.token_file = 'spotify_tokens.json'
        self.logger = logging.getLogger(__name__)
        
        # Validate configuration
        Config.validate()
    
    def get_auth_url(self) -> str:
        """Generate Spotify authorization URL"""
        params = {
            'client_id': self.client_id,
            'response_type': 'code',
            'redirect_uri': self.redirect_uri,
            'scope': self.scopes,
            'show_dialog': 'true'
        }
        
        auth_url = f"{Config.SPOTIFY_AUTH_URL}?{urllib.parse.urlencode(params)}"
        return auth_url
    
    def exchange_code_for_tokens(self, authorization_code: str) -> Optional[Dict[str, Any]]:
        """Exchange authorization code for access and refresh tokens"""
        try:
            # Prepare the request
            auth_header = base64.b64encode(
                f"{self.client_id}:{self.client_secret}".encode()
            ).decode()
            
            headers = {
                'Authorization': f'Basic {auth_header}',
                'Content-Type': 'application/x-www-form-urlencoded'
            }
            
            data = {
                'grant_type': 'authorization_code',
                'code': authorization_code,
                'redirect_uri': self.redirect_uri
            }
            
            response = requests.post(Config.SPOTIFY_TOKEN_URL, headers=headers, data=data)
            response.raise_for_status()
            
            token_data = response.json()
            
            # Add expiration timestamp
            token_data['expires_at'] = time.time() + token_data['expires_in']
            
            # Save tokens to file
            self.save_tokens(token_data)
            
            self.logger.info("Successfully obtained access tokens")
            return token_data
            
        except requests.exceptions.RequestException as e:
            self.logger.error(f"Error exchanging code for tokens: {e}")
            return None
    
    def refresh_access_token(self, refresh_token: str) -> Optional[Dict[str, Any]]:
        """Refresh the access token using refresh token"""
        try:
            auth_header = base64.b64encode(
                f"{self.client_id}:{self.client_secret}".encode()
            ).decode()
            
            headers = {
                'Authorization': f'Basic {auth_header}',
                'Content-Type': 'application/x-www-form-urlencoded'
            }
            
            data = {
                'grant_type': 'refresh_token',
                'refresh_token': refresh_token
            }
            
            response = requests.post(Config.SPOTIFY_TOKEN_URL, headers=headers, data=data)
            response.raise_for_status()
            
            token_data = response.json()
            
            # Add expiration timestamp
            token_data['expires_at'] = time.time() + token_data['expires_in']
            
            # If no new refresh token is provided, keep the old one
            if 'refresh_token' not in token_data:
                token_data['refresh_token'] = refresh_token
            
            # Save updated tokens
            self.save_tokens(token_data)
            
            self.logger.info("Successfully refreshed access token")
            return token_data
            
        except requests.exceptions.RequestException as e:
            self.logger.error(f"Error refreshing access token: {e}")
            return None
    
    def save_tokens(self, token_data: Dict[str, Any]) -> None:
        """Save tokens to file"""
        try:
            with open(self.token_file, 'w') as f:
                json.dump(token_data, f, indent=2)
            self.logger.debug("Tokens saved successfully")
        except Exception as e:
            self.logger.error(f"Error saving tokens: {e}")
    
    def load_tokens(self) -> Optional[Dict[str, Any]]:
        """Load tokens from file"""
        try:
            if os.path.exists(self.token_file):
                with open(self.token_file, 'r') as f:
                    return json.load(f)
        except Exception as e:
            self.logger.error(f"Error loading tokens: {e}")
        return None
    
    def is_token_expired(self, token_data: Dict[str, Any]) -> bool:
        """Check if access token is expired"""
        if 'expires_at' not in token_data:
            return True
        
        # Add 5 minute buffer
        return time.time() >= (token_data['expires_at'] - 300)
    
    def get_valid_access_token(self) -> Optional[str]:
        """Get a valid access token, refreshing if necessary"""
        token_data = self.load_tokens()
        
        if not token_data:
            self.logger.warning("No tokens found. Need to authenticate first.")
            return None
        
        if not self.is_token_expired(token_data):
            return token_data['access_token']
        
        # Token is expired, try to refresh
        if 'refresh_token' in token_data:
            refreshed_tokens = self.refresh_access_token(token_data['refresh_token'])
            if refreshed_tokens:
                return refreshed_tokens['access_token']
        
        self.logger.warning("Unable to refresh token. Need to re-authenticate.")
        return None
    
    def authenticate_interactive(self) -> bool:
        """Interactive authentication flow optimized for servers"""
        print("Spotify Tracker Authentication")
        print("=" * 40)

        # Generate and display auth URL
        auth_url = self.get_auth_url()
        print(f"Step 1: Visit this URL in your browser (from any device):")
        print(f"{auth_url}")
        print()
        print("Step 2: After authorizing, you'll be redirected to a URL like:")
        print(f"{self.redirect_uri}?code=...")
        print("(The page may not load - that's normal!)")
        print()
        print("Step 3: Copy the ENTIRE redirect URL from your browser's address bar")
        print("        and paste it below:")
        print()

        # Get authorization code from user
        while True:
            try:
                redirect_url = input("Paste the redirect URL here: ").strip()

                if not redirect_url:
                    print("Please enter the redirect URL.")
                    continue

                # Extract code from URL
                parsed_url = urllib.parse.urlparse(redirect_url)
                query_params = urllib.parse.parse_qs(parsed_url.query)

                if 'error' in query_params:
                    print(f"Authorization error: {query_params['error'][0]}")
                    return False

                if 'code' not in query_params:
                    print("Error: No authorization code found in URL")
                    print("Make sure you copied the complete URL that starts with:")
                    print(f"{self.redirect_uri}?code=...")
                    continue

                authorization_code = query_params['code'][0]
                print("Authorization code found! Exchanging for tokens...")

                # Exchange code for tokens
                tokens = self.exchange_code_for_tokens(authorization_code)

                if tokens:
                    print("✅ Authentication successful!")
                    print("The Spotify Tracker is now authorized to access your account.")
                    return True
                else:
                    print("❌ Authentication failed during token exchange!")
                    return False

            except KeyboardInterrupt:
                print("\nAuthentication cancelled by user.")
                return False
            except Exception as e:
                print(f"Error processing redirect URL: {e}")
                print("Please try again with the complete redirect URL.")
                continue
    
    def authenticate_with_server(self, timeout: int = 300) -> bool:
        """Automatic authentication using temporary HTTP server"""
        print("Spotify Tracker Authentication (Automatic Mode)")
        print("=" * 50)

        # Extract port from redirect URI
        try:
            parsed_uri = urllib.parse.urlparse(self.redirect_uri)
            port = parsed_uri.port or 8080
        except:
            port = 8080

        # Check if port is available
        if not self._is_port_available(port):
            print(f"❌ Port {port} is not available.")
            print("Please either:")
            print("1. Free up the port, or")
            print("2. Use manual authentication: python main.py --auth --manual")
            return False

        # Start HTTP server
        try:
            server = HTTPServer(('localhost', port), CallbackHandler)
            server.auth_code = None
            server.auth_error = None
            server.timeout = 1  # Short timeout for non-blocking

            # Start server in background thread
            server_thread = threading.Thread(target=self._run_server, args=(server, timeout))
            server_thread.daemon = True
            server_thread.start()

            print(f"✅ Started callback server on http://localhost:{port}")
            print()

            # Generate and display auth URL
            auth_url = self.get_auth_url()
            print("🔗 Please visit this URL to authorize the application:")
            print(f"{auth_url}")
            print()
            print("📱 You can open this URL on any device (phone, computer, etc.)")
            print("   After authorization, you'll be redirected automatically.")
            print()
            print(f"⏱️  Waiting up to {timeout} seconds for authorization...")

            # Wait for callback
            start_time = time.time()
            while time.time() - start_time < timeout:
                if server.auth_code:
                    print("✅ Authorization code received!")

                    # Exchange code for tokens
                    tokens = self.exchange_code_for_tokens(server.auth_code)

                    if tokens:
                        print("✅ Authentication successful!")
                        return True
                    else:
                        print("❌ Failed to exchange code for tokens")
                        return False

                elif server.auth_error:
                    print(f"❌ Authorization error: {server.auth_error}")
                    return False

                time.sleep(1)

            print("⏰ Authentication timed out. Please try again.")
            return False

        except Exception as e:
            print(f"❌ Error starting callback server: {e}")
            return False

        finally:
            try:
                server.shutdown()
            except:
                pass

    def _is_port_available(self, port: int) -> bool:
        """Check if a port is available"""
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.bind(('localhost', port))
                return True
        except:
            return False

    def _run_server(self, server, timeout):
        """Run HTTP server with timeout"""
        start_time = time.time()
        while time.time() - start_time < timeout:
            try:
                server.handle_request()
                if hasattr(server, 'auth_code') and server.auth_code:
                    break
                if hasattr(server, 'auth_error') and server.auth_error:
                    break
            except:
                break

    def is_authenticated(self) -> bool:
        """Check if user is authenticated and has valid tokens"""
        return self.get_valid_access_token() is not None
