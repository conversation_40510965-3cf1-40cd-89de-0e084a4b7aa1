# Spotify Tracker

A comprehensive Spotify listening tracker that monitors your music habits, generates detailed statistics, and creates reports. Designed to run continuously on Ubuntu servers.

## Features

- **Real-time Tracking**: Automatically fetches recently played tracks every 10 minutes
- **Top Lists**: Tracks your top songs and artists across different time periods (short, medium, long term)
- **Detailed Statistics**: Listening time, play counts, genre distribution, daily/hourly patterns
- **Comprehensive Reports**: Daily, weekly, and monthly reports in JSON and CSV formats
- **Health Monitoring**: Built-in monitoring and error recovery
- **Secure**: Uses OAuth 2.0 for Spotify authentication
- **Systemd Integration**: Runs as a proper Linux service

## What Data is Tracked

- **Recently Played Tracks**: Every song you listen to with timestamp
- **Top Tracks & Artists**: Your most played content across different time ranges
- **Listening Statistics**: 
  - Total listening time (minutes/hours)
  - Play counts per track/artist
  - Unique tracks and artists
  - Daily and hourly listening patterns
  - Genre distribution
- **Historical Data**: All data is stored locally in SQLite database

## Prerequisites

- Ubuntu Server (18.04 or later)
- Python 3.8+
- Spotify Premium account (recommended for full API access)
- Spotify Developer App credentials

## Quick Start

### Option 1: Web-Based Setup (Recommended)

The easiest way to set up Spotify Tracker is using the web interface:

1. **Transfer files to your server:**
   ```bash
   # Upload all project files to your server
   scp -r spotify-tracker/* user@your-server:~/spotify-tracker/
   ```

2. **Run the quick setup:**
   ```bash
   cd ~/spotify-tracker
   chmod +x quick_setup.sh
   ./quick_setup.sh
   ```

3. **Start the web setup interface:**
   ```bash
   python3 web_setup.py
   ```

4. **Complete setup in your browser:**
   - Open: `http://your-server-ip:1111`
   - Follow the step-by-step web interface
   - Enter your Spotify credentials
   - The interface handles everything automatically!

### Option 2: Manual Installation

### 1. Get Spotify API Credentials

1. Go to [Spotify Developer Dashboard](https://developer.spotify.com/dashboard)
2. Create a new app
3. Note down your `Client ID` and `Client Secret`
4. Add `http://your-server-ip:1111/callback` to your app's redirect URIs

### 2. Install on Ubuntu Server

```bash
# Clone or download the project files
git clone <repository-url>
cd spotify-tracker

# Make installation script executable
chmod +x install.sh

# Run installation (requires sudo)
sudo ./install.sh
```

### 3. Configure

Edit the configuration file:
```bash
sudo nano /opt/spotify-tracker/.env
```

Add your Spotify credentials:
```env
SPOTIFY_CLIENT_ID=your_client_id_here
SPOTIFY_CLIENT_SECRET=your_client_secret_here
SPOTIFY_REDIRECT_URI=http://localhost:8080/callback
```

### 4. Authenticate

```bash
# Authenticate with Spotify (one-time setup)
sudo -u spotify-tracker /opt/spotify-tracker/venv/bin/python /opt/spotify-tracker/main.py --auth
```

Follow the prompts to authorize the application with your Spotify account.

### 5. Start the Service

```bash
# Start the service
sudo systemctl start spotify-tracker

# Enable auto-start on boot
sudo systemctl enable spotify-tracker

# Check status
sudo systemctl status spotify-tracker
```

## Usage

### Service Management

```bash
# Start the service
sudo systemctl start spotify-tracker

# Stop the service
sudo systemctl stop spotify-tracker

# Restart the service
sudo systemctl restart spotify-tracker

# Check service status
sudo systemctl status spotify-tracker

# View logs
sudo journalctl -u spotify-tracker -f
```

### Manual Commands

```bash
# Switch to service user
sudo -u spotify-tracker -s

# Navigate to installation directory
cd /opt/spotify-tracker

# Run one-time data collection
./venv/bin/python main.py --once

# Show current statistics (last 30 days)
./venv/bin/python main.py --stats

# Show statistics for specific period
./venv/bin/python main.py --stats 7

# Generate reports
./venv/bin/python main.py --report daily
./venv/bin/python main.py --report weekly
./venv/bin/python main.py --report monthly

# Check system status
./venv/bin/python main.py --status
```

### Generated Files

- **Database**: `/opt/spotify-tracker/spotify_tracker.db`
- **Logs**: `/opt/spotify-tracker/logs/`
- **Reports**: `/opt/spotify-tracker/reports/`

## Configuration Options

Edit `/opt/spotify-tracker/.env`:

```env
# Spotify API Credentials
SPOTIFY_CLIENT_ID=your_client_id_here
SPOTIFY_CLIENT_SECRET=your_client_secret_here
SPOTIFY_REDIRECT_URI=http://localhost:8080/callback

# Tracking Configuration
FETCH_INTERVAL_MINUTES=10
MAX_RECENTLY_PLAYED=50

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=spotify_tracker.log
```

## Monitoring

### Health Checks

The system includes built-in health monitoring:

- API success/failure rates
- Database error tracking
- Authentication status
- Last successful data fetch
- Overall health score

### Logs

Logs are automatically rotated and stored in:
- Main log: `/opt/spotify-tracker/logs/spotify_tracker.log`
- Error log: `/opt/spotify-tracker/logs/error.log`
- API log: `/opt/spotify-tracker/logs/api.log`
- Database log: `/opt/spotify-tracker/logs/database.log`

### Scheduled Tasks

The service automatically runs:
- **Recently played fetch**: Every 10 minutes (configurable)
- **Top tracks/artists update**: Every 6 hours
- **Daily report generation**: Daily at 1:00 AM
- **Health checks**: Every 30 minutes

## Data Privacy

- All data is stored locally on your server
- No data is sent to external services (except Spotify API calls)
- You have full control over your listening data
- Database can be backed up or exported at any time

## Troubleshooting

### Service Won't Start

1. Check service status: `sudo systemctl status spotify-tracker`
2. Check logs: `sudo journalctl -u spotify-tracker -f`
3. Verify configuration: `sudo -u spotify-tracker cat /opt/spotify-tracker/.env`
4. Test authentication: `sudo -u spotify-tracker /opt/spotify-tracker/venv/bin/python /opt/spotify-tracker/main.py --status`

### Authentication Issues

1. Re-authenticate: `sudo -u spotify-tracker /opt/spotify-tracker/venv/bin/python /opt/spotify-tracker/main.py --auth`
2. Check Spotify app settings in developer dashboard
3. Verify redirect URI matches configuration

### No Data Being Collected

1. Check if you're actively listening to Spotify
2. Verify Spotify Premium account (some API features require Premium)
3. Check API rate limits in logs
4. Ensure service is running: `sudo systemctl status spotify-tracker`

## Uninstallation

```bash
# Stop and disable service
sudo systemctl stop spotify-tracker
sudo systemctl disable spotify-tracker

# Remove service file
sudo rm /etc/systemd/system/spotify-tracker.service
sudo systemctl daemon-reload

# Remove installation directory
sudo rm -rf /opt/spotify-tracker

# Remove user (optional)
sudo userdel spotify-tracker

# Remove logrotate configuration
sudo rm /etc/logrotate.d/spotify-tracker
```

## License

This project is open source. Please ensure you comply with Spotify's API Terms of Service when using this software.

## Support

For issues and questions:
1. Check the logs for error messages
2. Verify your Spotify API credentials
3. Ensure your Spotify account has the necessary permissions
4. Check that the service is running and healthy
