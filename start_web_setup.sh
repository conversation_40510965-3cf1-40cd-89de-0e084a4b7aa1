#!/bin/bash

# Spotify Tracker - Web Setup Launcher
# This script starts the web-based setup interface

echo "🎵 Spotify Tracker - Web Setup"
echo "==============================="

# Check if running as regular user (not root)
if [[ $EUID -eq 0 ]]; then
   echo "❌ Please run this script as a regular user (not with sudo)"
   exit 1
fi

# Get the server's IP address
SERVER_IP=$(hostname -I | awk '{print $1}')

echo "🔍 Detected server IP: $SERVER_IP"
echo ""

# Create virtual environment and install Flask
echo "📦 Setting up Python environment..."

# Install python3-venv if not already installed
sudo apt update -qq
sudo apt install -y python3-venv python3-full

# Create virtual environment if it doesn't exist
if [ ! -d "venv" ]; then
    echo "🐍 Creating virtual environment..."
    python3 -m venv venv
fi

# Install Flask and requests in virtual environment
echo "📦 Installing Flask and dependencies..."
./venv/bin/pip install flask requests --quiet

# Check if web_setup.py exists
if [ ! -f "web_setup.py" ]; then
    echo "❌ web_setup.py not found in current directory"
    echo "Please make sure you're in the spotify-tracker directory with all files"
    exit 1
fi

echo "🌐 Starting web setup interface..."
echo ""
echo "📱 Open your browser and go to:"
echo "   http://$SERVER_IP:8888"
echo ""
echo "💡 The web interface will guide you through:"
echo "   1. Spotify app configuration"
echo "   2. Dependency installation"
echo "   3. Authentication"
echo "   4. Service setup"
echo ""
echo "⏹️  Press Ctrl+C to stop the web server"
echo ""

# Start the web setup using virtual environment
./venv/bin/python web_setup.py
