import logging
import logging.handlers
import os
import sys
from datetime import datetime
from pathlib import Path
from config import Config

def setup_logging():
    """Setup comprehensive logging configuration"""
    
    # Create logs directory if it doesn't exist
    logs_dir = Path('logs')
    logs_dir.mkdir(exist_ok=True)
    
    # Configure root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, Config.LOG_LEVEL.upper(), logging.INFO))
    
    # Clear any existing handlers
    root_logger.handlers.clear()
    
    # Create formatters
    detailed_formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    simple_formatter = logging.Formatter(
        '%(asctime)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # Console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(simple_formatter)
    root_logger.addHandler(console_handler)
    
    # Main log file handler (rotating)
    main_log_file = logs_dir / Config.LOG_FILE
    file_handler = logging.handlers.RotatingFileHandler(
        main_log_file,
        maxBytes=10*1024*1024,  # 10MB
        backupCount=5,
        encoding='utf-8'
    )
    file_handler.setLevel(logging.DEBUG)
    file_handler.setFormatter(detailed_formatter)
    root_logger.addHandler(file_handler)
    
    # Error log file handler
    error_log_file = logs_dir / 'error.log'
    error_handler = logging.handlers.RotatingFileHandler(
        error_log_file,
        maxBytes=5*1024*1024,  # 5MB
        backupCount=3,
        encoding='utf-8'
    )
    error_handler.setLevel(logging.ERROR)
    error_handler.setFormatter(detailed_formatter)
    root_logger.addHandler(error_handler)
    
    # API log file handler (for API-specific logs)
    api_log_file = logs_dir / 'api.log'
    api_handler = logging.handlers.RotatingFileHandler(
        api_log_file,
        maxBytes=5*1024*1024,  # 5MB
        backupCount=3,
        encoding='utf-8'
    )
    api_handler.setLevel(logging.DEBUG)
    api_handler.setFormatter(detailed_formatter)
    
    # Add API handler to specific loggers
    api_loggers = ['spotify_api', 'spotify_auth', 'scheduler']
    for logger_name in api_loggers:
        logger = logging.getLogger(logger_name)
        logger.addHandler(api_handler)
    
    # Database log file handler
    db_log_file = logs_dir / 'database.log'
    db_handler = logging.handlers.RotatingFileHandler(
        db_log_file,
        maxBytes=5*1024*1024,  # 5MB
        backupCount=3,
        encoding='utf-8'
    )
    db_handler.setLevel(logging.DEBUG)
    db_handler.setFormatter(detailed_formatter)
    
    # Add DB handler to database logger
    db_logger = logging.getLogger('database')
    db_logger.addHandler(db_handler)
    
    # Suppress some noisy third-party loggers
    logging.getLogger('urllib3').setLevel(logging.WARNING)
    logging.getLogger('requests').setLevel(logging.WARNING)
    
    logging.info("Logging system initialized")
    logging.info(f"Log level: {Config.LOG_LEVEL}")
    logging.info(f"Main log file: {main_log_file}")
    logging.info(f"Error log file: {error_log_file}")

class PerformanceMonitor:
    """Simple performance monitoring utility"""
    
    def __init__(self, logger_name: str = None):
        self.logger = logging.getLogger(logger_name or __name__)
        self.start_time = None
        self.operation_name = None
    
    def start(self, operation_name: str):
        """Start monitoring an operation"""
        self.operation_name = operation_name
        self.start_time = datetime.now()
        self.logger.debug(f"Started operation: {operation_name}")
    
    def end(self, success: bool = True, additional_info: str = None):
        """End monitoring and log results"""
        if not self.start_time or not self.operation_name:
            self.logger.warning("Performance monitor not properly initialized")
            return
        
        end_time = datetime.now()
        duration = (end_time - self.start_time).total_seconds()
        
        status = "SUCCESS" if success else "FAILED"
        message = f"Operation {self.operation_name} {status} in {duration:.2f}s"
        
        if additional_info:
            message += f" - {additional_info}"
        
        if success:
            self.logger.info(message)
        else:
            self.logger.error(message)
        
        # Reset
        self.start_time = None
        self.operation_name = None
        
        return duration

class HealthMonitor:
    """System health monitoring"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.metrics = {
            'api_calls_success': 0,
            'api_calls_failed': 0,
            'tracks_stored': 0,
            'database_errors': 0,
            'auth_failures': 0,
            'last_successful_fetch': None,
            'uptime_start': datetime.now()
        }
    
    def record_api_success(self):
        """Record successful API call"""
        self.metrics['api_calls_success'] += 1
        self.metrics['last_successful_fetch'] = datetime.now()
    
    def record_api_failure(self):
        """Record failed API call"""
        self.metrics['api_calls_failed'] += 1
    
    def record_tracks_stored(self, count: int):
        """Record number of tracks stored"""
        self.metrics['tracks_stored'] += count
    
    def record_database_error(self):
        """Record database error"""
        self.metrics['database_errors'] += 1
    
    def record_auth_failure(self):
        """Record authentication failure"""
        self.metrics['auth_failures'] += 1
    
    def get_health_status(self) -> dict:
        """Get current health status"""
        now = datetime.now()
        uptime = now - self.metrics['uptime_start']
        
        total_api_calls = self.metrics['api_calls_success'] + self.metrics['api_calls_failed']
        success_rate = (self.metrics['api_calls_success'] / max(total_api_calls, 1)) * 100
        
        status = {
            'uptime_seconds': uptime.total_seconds(),
            'uptime_hours': uptime.total_seconds() / 3600,
            'api_calls_total': total_api_calls,
            'api_calls_success': self.metrics['api_calls_success'],
            'api_calls_failed': self.metrics['api_calls_failed'],
            'api_success_rate': round(success_rate, 2),
            'tracks_stored': self.metrics['tracks_stored'],
            'database_errors': self.metrics['database_errors'],
            'auth_failures': self.metrics['auth_failures'],
            'last_successful_fetch': self.metrics['last_successful_fetch'].isoformat() if self.metrics['last_successful_fetch'] else None,
            'health_score': self._calculate_health_score()
        }
        
        return status
    
    def _calculate_health_score(self) -> str:
        """Calculate overall health score"""
        total_api_calls = self.metrics['api_calls_success'] + self.metrics['api_calls_failed']
        
        if total_api_calls == 0:
            return "UNKNOWN"
        
        success_rate = (self.metrics['api_calls_success'] / total_api_calls) * 100
        
        # Check for recent activity
        if self.metrics['last_successful_fetch']:
            time_since_success = datetime.now() - self.metrics['last_successful_fetch']
            if time_since_success.total_seconds() > 7200:  # 2 hours
                return "UNHEALTHY"
        
        # Score based on success rate and error counts
        if success_rate >= 95 and self.metrics['database_errors'] < 5 and self.metrics['auth_failures'] < 3:
            return "HEALTHY"
        elif success_rate >= 80 and self.metrics['database_errors'] < 10:
            return "WARNING"
        else:
            return "UNHEALTHY"
    
    def log_health_summary(self):
        """Log current health summary"""
        status = self.get_health_status()
        
        self.logger.info("=== HEALTH SUMMARY ===")
        self.logger.info(f"Health Score: {status['health_score']}")
        self.logger.info(f"Uptime: {status['uptime_hours']:.1f} hours")
        self.logger.info(f"API Success Rate: {status['api_success_rate']}%")
        self.logger.info(f"Tracks Stored: {status['tracks_stored']}")
        self.logger.info(f"Database Errors: {status['database_errors']}")
        self.logger.info(f"Last Successful Fetch: {status['last_successful_fetch']}")
        self.logger.info("=====================")

# Global health monitor instance
health_monitor = HealthMonitor()
