# 🎵 Spotify Tracker - Getting Started

## Super Easy Web Setup (Recommended)

### Step 1: Transfer Files to Your Server

Using WinSCP, <PERSON><PERSON><PERSON>, or scp, transfer all the project files to your Ubuntu server:

```bash
# Create directory on server
mkdir ~/spotify-tracker

# Upload all files to ~/spotify-tracker/
```

### Step 2: Start Web Setup

SSH into your server and run:

```bash
cd ~/spotify-tracker
chmod +x start_web_setup.sh
./start_web_setup.sh
```

### Step 3: Open Web Interface

The script will show you the URL to open in your browser:
```
Open your browser and go to:
   http://*************:8888
```

### Step 4: Follow the Web Interface

The web interface will guide you through 5 simple steps:

1. **Spotify App Configuration** - Enter your Client ID and Secret
2. **Install Dependencies** - Click to install automatically  
3. **Authenticate** - Authorize with Spotify (popup window)
4. **Start Service** - Click to start the tracker
5. **Setup Complete!** - Your tracker is now running

## What You Need Before Starting

### Spotify Developer App

1. Go to [Spotify Developer Dashboard](https://developer.spotify.com/dashboard)
2. Click "Create app"
3. Fill in:
   - **App name:** My Spotify Tracker
   - **Description:** Personal listening tracker
   - **Website:** http://localhost
   - **Redirect URI:** `http://*************:8888/callback` (replace with your server IP)
   - **API:** Check "Web API"
4. Save and copy your **Client ID** and **Client Secret**

## Files Included

- `web_setup.py` - Web-based setup interface
- `start_web_setup.sh` - Easy launcher script
- `main.py` - Main application
- `spotify_auth.py` - Authentication system
- `spotify_api.py` - Spotify API integration
- `database.py` - Database management
- `statistics.py` - Analytics and statistics
- `reports.py` - Report generation
- `scheduler.py` - Background task scheduler
- `config.py` - Configuration management
- `requirements.txt` - Python dependencies
- `install.sh` - Manual installation script (alternative)
- `README.md` - Detailed documentation

## After Setup

Once setup is complete, your Spotify Tracker will:

- ✅ **Automatically collect** your listening data every 10 minutes
- ✅ **Generate daily reports** at 1:00 AM
- ✅ **Track statistics** like listening time, top tracks, artists, genres
- ✅ **Run as a service** that starts automatically on boot

### Useful Commands

```bash
# Check service status
sudo systemctl status spotify-tracker

# View live logs
sudo journalctl -u spotify-tracker -f

# Generate reports manually
sudo -u spotify-tracker /opt/spotify-tracker/venv/bin/python /opt/spotify-tracker/main.py --report daily

# View statistics
sudo -u spotify-tracker /opt/spotify-tracker/venv/bin/python /opt/spotify-tracker/main.py --stats 30
```

## Troubleshooting

### Web Setup Won't Start
```bash
# Install Flask manually
pip3 install flask requests --user

# Try again
./start_web_setup.sh
```

### Can't Access Web Interface
- Make sure port 8888 is not blocked by firewall
- Check your server's IP address: `hostname -I`
- Try accessing from the same network

### Authentication Issues - "INVALID_CLIENT: Insecure redirect URI"

This is the most common issue. Here are solutions:

**Option 1: Use Alternative Authentication**
```bash
# Use the standalone auth helper
python3 auth_helper.py your_client_id your_client_secret http://your-server-ip:8888/callback
```

**Option 2: Update Spotify App Settings**
1. Go to [Spotify Developer Dashboard](https://developer.spotify.com/dashboard)
2. Click your app → Settings
3. Make sure redirect URI is exactly: `http://your-server-ip:8888/callback`
4. Try different ports: 8888, 3000, 8080, 8000

**Option 3: Use Localhost (if accessing from same machine)**
- Change redirect URI to: `http://localhost:8888/callback`
- Access web interface via: `http://localhost:8888`

### Other Authentication Issues
- Verify your Spotify app redirect URI matches exactly
- Make sure you're using the correct Client ID and Secret
- Check that your Spotify app has "Web API" enabled
- Try clearing browser cookies for Spotify

## Need Help?

1. Check the logs: `sudo journalctl -u spotify-tracker -f`
2. Verify service status: `sudo systemctl status spotify-tracker`
3. Review the detailed README.md for more information

The web setup makes everything automatic - just follow the steps in your browser!
