#!/usr/bin/env python3
"""
Spotify Authentication Helper
Provides alternative authentication methods for servers
"""

import base64
import json
import urllib.parse
import requests
from http.server import HTTPServer, BaseHTTPRequestHandler
import threading
import time
import webbrowser

class AuthCallbackHandler(BaseHTTPRequestHandler):
    """Handle OAuth callback"""
    
    def do_GET(self):
        """Handle GET request for OAuth callback"""
        parsed_path = urllib.parse.urlparse(self.path)
        query_params = urllib.parse.parse_qs(parsed_path.query)
        
        if 'code' in query_params:
            self.server.auth_code = query_params['code'][0]
            self.send_response(200)
            self.send_header('Content-type', 'text/html')
            self.end_headers()
            self.wfile.write(b'''
            <html>
            <head><title>Spotify Tracker - Authentication Successful</title></head>
            <body style="font-family: Arial, sans-serif; text-align: center; margin-top: 100px;">
                <h1 style="color: #1DB954;">✅ Authentication Successful!</h1>
                <p>You can now close this window and return to your terminal.</p>
                <p>The Spotify Tracker is now authorized to access your account.</p>
                <script>setTimeout(function(){ window.close(); }, 3000);</script>
            </body>
            </html>
            ''')
        elif 'error' in query_params:
            self.server.auth_error = query_params['error'][0]
            self.send_response(400)
            self.send_header('Content-type', 'text/html')
            self.end_headers()
            error_msg = query_params['error'][0]
            self.wfile.write(f'''
            <html>
            <head><title>Spotify Tracker - Authentication Error</title></head>
            <body style="font-family: Arial, sans-serif; text-align: center; margin-top: 100px;">
                <h1 style="color: #ff0000;">❌ Authentication Error</h1>
                <p>Error: {error_msg}</p>
                <p>Please try again or check your configuration.</p>
                <script>setTimeout(function(){{ window.close(); }}, 5000);</script>
            </body>
            </html>
            '''.encode())
        else:
            self.send_response(400)
            self.send_header('Content-type', 'text/html')
            self.end_headers()
            self.wfile.write(b'Invalid callback')
    
    def log_message(self, format, *args):
        """Suppress default HTTP server logging"""
        pass

def authenticate_spotify(client_id, client_secret, redirect_uri, port=8888):
    """
    Authenticate with Spotify using local HTTP server
    """
    print("🎵 Spotify Authentication Helper")
    print("=" * 40)
    
    # Start local HTTP server
    try:
        server = HTTPServer(('', port), AuthCallbackHandler)
        server.auth_code = None
        server.auth_error = None
        server.timeout = 1
        
        print(f"✅ Started callback server on port {port}")
        
        # Generate auth URL
        scopes = 'user-read-recently-played user-top-read user-read-playback-state user-read-currently-playing'
        encoded_redirect_uri = urllib.parse.quote(redirect_uri, safe='')
        
        auth_url = f"https://accounts.spotify.com/authorize?client_id={client_id}&response_type=code&redirect_uri={encoded_redirect_uri}&scope={scopes}&show_dialog=true"
        
        print("\n🔗 Please visit this URL to authorize the application:")
        print(f"{auth_url}")
        print("\n📱 You can open this URL on any device (phone, computer, etc.)")
        print("   After authorization, you'll be redirected automatically.")
        print("\n⏱️  Waiting for authorization...")
        
        # Start server in background
        server_thread = threading.Thread(target=run_server, args=(server, 300))
        server_thread.daemon = True
        server_thread.start()
        
        # Wait for callback
        start_time = time.time()
        while time.time() - start_time < 300:  # 5 minute timeout
            if server.auth_code:
                print("✅ Authorization code received!")
                
                # Exchange code for tokens
                tokens = exchange_code_for_tokens(client_id, client_secret, server.auth_code, redirect_uri)
                
                if tokens:
                    print("✅ Authentication successful!")
                    
                    # Save tokens
                    with open('spotify_tokens.json', 'w') as f:
                        json.dump(tokens, f, indent=2)
                    
                    return tokens
                else:
                    print("❌ Failed to exchange code for tokens")
                    return None
            
            elif server.auth_error:
                print(f"❌ Authorization error: {server.auth_error}")
                return None
            
            time.sleep(1)
        
        print("⏰ Authentication timed out. Please try again.")
        return None
        
    except Exception as e:
        print(f"❌ Error starting callback server: {e}")
        return None
    
    finally:
        try:
            server.shutdown()
        except:
            pass

def run_server(server, timeout):
    """Run HTTP server with timeout"""
    start_time = time.time()
    while time.time() - start_time < timeout:
        try:
            server.handle_request()
            if hasattr(server, 'auth_code') and server.auth_code:
                break
            if hasattr(server, 'auth_error') and server.auth_error:
                break
        except:
            break

def exchange_code_for_tokens(client_id, client_secret, auth_code, redirect_uri):
    """Exchange authorization code for access tokens"""
    try:
        auth_header = base64.b64encode(f"{client_id}:{client_secret}".encode()).decode()
        
        headers = {
            'Authorization': f'Basic {auth_header}',
            'Content-Type': 'application/x-www-form-urlencoded'
        }
        
        data = {
            'grant_type': 'authorization_code',
            'code': auth_code,
            'redirect_uri': redirect_uri
        }
        
        response = requests.post('https://accounts.spotify.com/api/token', headers=headers, data=data)
        
        if response.status_code == 200:
            tokens = response.json()
            tokens['expires_at'] = time.time() + tokens['expires_in']
            return tokens
        else:
            print(f"Token exchange failed: {response.status_code} - {response.text}")
            return None
            
    except Exception as e:
        print(f"Error exchanging code for tokens: {e}")
        return None

if __name__ == '__main__':
    import sys
    
    if len(sys.argv) != 4:
        print("Usage: python3 auth_helper.py <client_id> <client_secret> <redirect_uri>")
        sys.exit(1)
    
    client_id = sys.argv[1]
    client_secret = sys.argv[2]
    redirect_uri = sys.argv[3]
    
    tokens = authenticate_spotify(client_id, client_secret, redirect_uri)
    
    if tokens:
        print("\n✅ Authentication completed successfully!")
        print("Tokens saved to spotify_tokens.json")
    else:
        print("\n❌ Authentication failed!")
        sys.exit(1)
