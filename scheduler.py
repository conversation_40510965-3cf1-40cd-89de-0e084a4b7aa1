import logging
import schedule
import time
import threading
from datetime import datetime, timed<PERSON>ta
from typing import Callable, Optional
from spotify_api import SpotifyAPI
from database import SpotifyDatabase
from reports import SpotifyReports
from config import Config

class SpotifyScheduler:
    def __init__(self):
        self.api = SpotifyAPI()
        self.db = SpotifyDatabase()
        self.reports = SpotifyReports(self.db)
        self.logger = logging.getLogger(__name__)
        self.running = False
        self.scheduler_thread = None
        
        # Error tracking
        self.consecutive_errors = 0
        self.max_consecutive_errors = 5
        self.last_successful_fetch = None
        
    def fetch_recently_played_job(self):
        """Job to fetch recently played tracks"""
        try:
            self.logger.info("Starting recently played tracks fetch")
            
            if not self.api.auth.is_authenticated():
                self.logger.error("Not authenticated with Spotify")
                self.consecutive_errors += 1
                return
            
            # Fetch and store recently played tracks
            stored_count = self.api.fetch_and_store_recently_played()
            
            if stored_count >= 0:  # 0 is valid (no new tracks)
                self.logger.info(f"Successfully fetched recently played tracks. New tracks: {stored_count}")
                self.consecutive_errors = 0
                self.last_successful_fetch = datetime.now()
                
                # Update daily statistics
                today = datetime.now().strftime('%Y-%m-%d')
                self.db.update_daily_statistics(today)
                
            else:
                self.logger.error("Failed to fetch recently played tracks")
                self.consecutive_errors += 1
                
        except Exception as e:
            self.logger.error(f"Error in recently played fetch job: {e}")
            self.consecutive_errors += 1
    
    def fetch_top_data_job(self):
        """Job to fetch top tracks and artists"""
        try:
            self.logger.info("Starting top data fetch")
            
            if not self.api.auth.is_authenticated():
                self.logger.error("Not authenticated with Spotify")
                return
            
            # Fetch and store top data for all time ranges
            results = self.api.fetch_and_store_top_data()
            
            success_count = sum(1 for success in results.values() if success)
            total_count = len(results)
            
            self.logger.info(f"Top data fetch completed. Success: {success_count}/{total_count}")
            
            if success_count > 0:
                self.consecutive_errors = 0
            else:
                self.consecutive_errors += 1
                
        except Exception as e:
            self.logger.error(f"Error in top data fetch job: {e}")
            self.consecutive_errors += 1
    
    def generate_daily_report_job(self):
        """Job to generate daily report"""
        try:
            self.logger.info("Generating daily report")
            
            yesterday = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')
            results = self.reports.generate_and_export_daily_report(yesterday)
            
            if results:
                self.logger.info(f"Daily report generated successfully: {results}")
            else:
                self.logger.warning("Daily report generation returned no results")
                
        except Exception as e:
            self.logger.error(f"Error generating daily report: {e}")
    
    def health_check_job(self):
        """Job to perform health checks"""
        try:
            # Check authentication status
            if not self.api.auth.is_authenticated():
                self.logger.warning("Authentication expired or invalid")
                return
            
            # Check for too many consecutive errors
            if self.consecutive_errors >= self.max_consecutive_errors:
                self.logger.error(f"Too many consecutive errors ({self.consecutive_errors}). System may need attention.")
                # Could implement notification system here
            
            # Check if we haven't had a successful fetch in too long
            if self.last_successful_fetch:
                time_since_success = datetime.now() - self.last_successful_fetch
                if time_since_success > timedelta(hours=2):
                    self.logger.warning(f"No successful fetch in {time_since_success}. Last success: {self.last_successful_fetch}")
            
            # Log current status
            self.logger.info(f"Health check: Consecutive errors: {self.consecutive_errors}, Last success: {self.last_successful_fetch}")
            
        except Exception as e:
            self.logger.error(f"Error in health check job: {e}")
    
    def setup_schedule(self):
        """Setup the scheduled jobs"""
        # Clear any existing schedule
        schedule.clear()
        
        # Recently played tracks - every 10 minutes (configurable)
        schedule.every(Config.FETCH_INTERVAL_MINUTES).minutes.do(self.fetch_recently_played_job)
        
        # Top tracks and artists - every 6 hours
        schedule.every(6).hours.do(self.fetch_top_data_job)
        
        # Daily report generation - every day at 1 AM
        schedule.every().day.at("01:00").do(self.generate_daily_report_job)
        
        # Health check - every 30 minutes
        schedule.every(30).minutes.do(self.health_check_job)
        
        self.logger.info("Scheduled jobs configured:")
        self.logger.info(f"- Recently played: every {Config.FETCH_INTERVAL_MINUTES} minutes")
        self.logger.info("- Top data: every 6 hours")
        self.logger.info("- Daily reports: daily at 1:00 AM")
        self.logger.info("- Health checks: every 30 minutes")
    
    def run_scheduler(self):
        """Run the scheduler in a loop"""
        self.logger.info("Starting scheduler loop")
        
        while self.running:
            try:
                schedule.run_pending()
                time.sleep(1)
            except KeyboardInterrupt:
                self.logger.info("Scheduler interrupted by user")
                break
            except Exception as e:
                self.logger.error(f"Error in scheduler loop: {e}")
                time.sleep(5)  # Wait a bit before continuing
        
        self.logger.info("Scheduler loop stopped")
    
    def start(self, run_initial_fetch: bool = True):
        """Start the scheduler"""
        if self.running:
            self.logger.warning("Scheduler is already running")
            return
        
        # Check authentication before starting
        if not self.api.auth.is_authenticated():
            self.logger.error("Not authenticated with Spotify. Please authenticate first.")
            return False
        
        self.logger.info("Starting Spotify Tracker Scheduler")
        
        # Setup scheduled jobs
        self.setup_schedule()
        
        # Run initial fetch if requested
        if run_initial_fetch:
            self.logger.info("Running initial data fetch")
            self.fetch_recently_played_job()
            self.fetch_top_data_job()
        
        # Start scheduler in a separate thread
        self.running = True
        self.scheduler_thread = threading.Thread(target=self.run_scheduler, daemon=True)
        self.scheduler_thread.start()
        
        self.logger.info("Scheduler started successfully")
        return True
    
    def stop(self):
        """Stop the scheduler"""
        if not self.running:
            self.logger.warning("Scheduler is not running")
            return
        
        self.logger.info("Stopping scheduler")
        self.running = False
        
        if self.scheduler_thread and self.scheduler_thread.is_alive():
            self.scheduler_thread.join(timeout=5)
        
        schedule.clear()
        self.logger.info("Scheduler stopped")
    
    def run_job_now(self, job_name: str):
        """Run a specific job immediately"""
        jobs = {
            'recently_played': self.fetch_recently_played_job,
            'top_data': self.fetch_top_data_job,
            'daily_report': self.generate_daily_report_job,
            'health_check': self.health_check_job
        }
        
        if job_name not in jobs:
            self.logger.error(f"Unknown job: {job_name}. Available jobs: {list(jobs.keys())}")
            return False
        
        self.logger.info(f"Running job '{job_name}' manually")
        try:
            jobs[job_name]()
            return True
        except Exception as e:
            self.logger.error(f"Error running job '{job_name}': {e}")
            return False
    
    def get_status(self) -> dict:
        """Get current scheduler status"""
        return {
            'running': self.running,
            'consecutive_errors': self.consecutive_errors,
            'last_successful_fetch': self.last_successful_fetch.isoformat() if self.last_successful_fetch else None,
            'authenticated': self.api.auth.is_authenticated(),
            'scheduled_jobs': len(schedule.jobs),
            'next_run': schedule.next_run().isoformat() if schedule.jobs else None
        }
    
    def is_running(self) -> bool:
        """Check if scheduler is running"""
        return self.running and self.scheduler_thread and self.scheduler_thread.is_alive()
