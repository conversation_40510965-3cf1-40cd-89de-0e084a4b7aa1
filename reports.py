import json
import csv
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from pathlib import Path
from statistics import SpotifyStatistics
from database import SpotifyDatabase

class SpotifyReports:
    def __init__(self, db: SpotifyDatabase = None):
        self.db = db or SpotifyDatabase()
        self.stats = SpotifyStatistics(self.db)
        self.logger = logging.getLogger(__name__)
        
        # Create reports directory if it doesn't exist
        self.reports_dir = Path('reports')
        self.reports_dir.mkdir(exist_ok=True)
    
    def generate_daily_report(self, date: str = None) -> Dict[str, Any]:
        """Generate a comprehensive daily report"""
        if not date:
            date = datetime.now().strftime('%Y-%m-%d')
        
        self.logger.info(f"Generating daily report for {date}")
        
        # Get daily statistics
        listening_stats = self.stats.get_listening_time_stats(date, date)
        top_tracks = self.stats.get_top_tracks_by_plays(10, date, date)
        top_artists = self.stats.get_top_artists_by_plays(10, date, date)
        genre_distribution = self.stats.get_genre_distribution(10, date, date)
        
        report = {
            'report_type': 'daily',
            'date': date,
            'generated_at': datetime.now().isoformat(),
            'summary': {
                'total_plays': listening_stats.get('total_plays', 0),
                'total_listening_time_minutes': listening_stats.get('total_listening_time_minutes', 0),
                'total_listening_time_hours': listening_stats.get('total_listening_time_hours', 0),
                'unique_tracks': listening_stats.get('unique_tracks', 0),
                'unique_artists': len(top_artists) if top_artists else 0
            },
            'listening_stats': listening_stats,
            'top_tracks': top_tracks,
            'top_artists': top_artists,
            'genre_distribution': genre_distribution
        }
        
        return report
    
    def generate_weekly_report(self, end_date: str = None) -> Dict[str, Any]:
        """Generate a comprehensive weekly report"""
        if not end_date:
            end_date = datetime.now().strftime('%Y-%m-%d')
        
        start_date = (datetime.strptime(end_date, '%Y-%m-%d') - timedelta(days=6)).strftime('%Y-%m-%d')
        
        self.logger.info(f"Generating weekly report for {start_date} to {end_date}")
        
        # Get weekly statistics
        listening_stats = self.stats.get_listening_time_stats(start_date, end_date)
        top_tracks = self.stats.get_top_tracks_by_plays(20, start_date, end_date)
        top_artists = self.stats.get_top_artists_by_plays(20, start_date, end_date)
        daily_pattern = self.stats.get_daily_listening_pattern(7)
        hourly_pattern = self.stats.get_hourly_listening_pattern(7)
        genre_distribution = self.stats.get_genre_distribution(15, start_date, end_date)
        
        report = {
            'report_type': 'weekly',
            'start_date': start_date,
            'end_date': end_date,
            'generated_at': datetime.now().isoformat(),
            'summary': {
                'total_plays': listening_stats.get('total_plays', 0),
                'total_listening_time_hours': listening_stats.get('total_listening_time_hours', 0),
                'average_plays_per_day': listening_stats.get('average_plays_per_day', 0),
                'unique_tracks': listening_stats.get('unique_tracks', 0),
                'unique_artists': len(top_artists) if top_artists else 0,
                'active_days': listening_stats.get('active_days', 0)
            },
            'listening_stats': listening_stats,
            'top_tracks': top_tracks,
            'top_artists': top_artists,
            'daily_pattern': daily_pattern,
            'hourly_pattern': hourly_pattern,
            'genre_distribution': genre_distribution
        }
        
        return report
    
    def generate_monthly_report(self, year: int = None, month: int = None) -> Dict[str, Any]:
        """Generate a comprehensive monthly report"""
        if not year or not month:
            now = datetime.now()
            year = year or now.year
            month = month or now.month
        
        # Calculate date range for the month
        start_date = datetime(year, month, 1).strftime('%Y-%m-%d')
        if month == 12:
            end_date = datetime(year + 1, 1, 1) - timedelta(days=1)
        else:
            end_date = datetime(year, month + 1, 1) - timedelta(days=1)
        end_date = end_date.strftime('%Y-%m-%d')
        
        self.logger.info(f"Generating monthly report for {year}-{month:02d}")
        
        # Calculate days in month for patterns
        days_in_month = (datetime.strptime(end_date, '%Y-%m-%d') - datetime.strptime(start_date, '%Y-%m-%d')).days + 1
        
        # Get monthly statistics
        listening_stats = self.stats.get_listening_time_stats(start_date, end_date)
        top_tracks = self.stats.get_top_tracks_by_plays(50, start_date, end_date)
        top_artists = self.stats.get_top_artists_by_plays(30, start_date, end_date)
        daily_pattern = self.stats.get_daily_listening_pattern(days_in_month)
        hourly_pattern = self.stats.get_hourly_listening_pattern(days_in_month)
        genre_distribution = self.stats.get_genre_distribution(20, start_date, end_date)
        
        report = {
            'report_type': 'monthly',
            'year': year,
            'month': month,
            'start_date': start_date,
            'end_date': end_date,
            'generated_at': datetime.now().isoformat(),
            'summary': {
                'total_plays': listening_stats.get('total_plays', 0),
                'total_listening_time_hours': listening_stats.get('total_listening_time_hours', 0),
                'average_plays_per_day': listening_stats.get('average_plays_per_day', 0),
                'unique_tracks': listening_stats.get('unique_tracks', 0),
                'unique_artists': len(top_artists) if top_artists else 0,
                'active_days': listening_stats.get('active_days', 0),
                'days_in_month': days_in_month
            },
            'listening_stats': listening_stats,
            'top_tracks': top_tracks,
            'top_artists': top_artists,
            'daily_pattern': daily_pattern,
            'hourly_pattern': hourly_pattern,
            'genre_distribution': genre_distribution
        }
        
        return report
    
    def export_to_json(self, data: Dict[str, Any], filename: str = None) -> str:
        """Export data to JSON file"""
        if not filename:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            report_type = data.get('report_type', 'report')
            filename = f"{report_type}_{timestamp}.json"
        
        filepath = self.reports_dir / filename
        
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"Report exported to JSON: {filepath}")
            return str(filepath)
            
        except Exception as e:
            self.logger.error(f"Error exporting to JSON: {e}")
            return None
    
    def export_top_tracks_to_csv(self, tracks: List[Dict[str, Any]], filename: str = None) -> str:
        """Export top tracks to CSV file"""
        if not tracks:
            return None
        
        if not filename:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"top_tracks_{timestamp}.csv"
        
        filepath = self.reports_dir / filename
        
        try:
            with open(filepath, 'w', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                
                # Write header
                writer.writerow([
                    'Track Name', 'Artists', 'Album', 'Play Count',
                    'Total Listening Time (minutes)', 'Track Duration (minutes)', 'Last Played'
                ])
                
                # Write data
                for track in tracks:
                    writer.writerow([
                        track.get('track_name', ''),
                        track.get('artists', ''),
                        track.get('album_name', ''),
                        track.get('play_count', 0),
                        track.get('total_listening_time_minutes', 0),
                        track.get('track_duration_minutes', 0),
                        track.get('last_played', '')
                    ])
            
            self.logger.info(f"Top tracks exported to CSV: {filepath}")
            return str(filepath)
            
        except Exception as e:
            self.logger.error(f"Error exporting top tracks to CSV: {e}")
            return None
    
    def export_top_artists_to_csv(self, artists: List[Dict[str, Any]], filename: str = None) -> str:
        """Export top artists to CSV file"""
        if not artists:
            return None
        
        if not filename:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"top_artists_{timestamp}.csv"
        
        filepath = self.reports_dir / filename
        
        try:
            with open(filepath, 'w', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                
                # Write header
                writer.writerow([
                    'Artist Name', 'Genres', 'Play Count', 'Unique Tracks',
                    'Total Listening Time (hours)', 'Popularity', 'Last Played'
                ])
                
                # Write data
                for artist in artists:
                    writer.writerow([
                        artist.get('artist_name', ''),
                        ', '.join(artist.get('genres', [])),
                        artist.get('play_count', 0),
                        artist.get('unique_tracks', 0),
                        artist.get('total_listening_time_hours', 0),
                        artist.get('popularity', ''),
                        artist.get('last_played', '')
                    ])
            
            self.logger.info(f"Top artists exported to CSV: {filepath}")
            return str(filepath)
            
        except Exception as e:
            self.logger.error(f"Error exporting top artists to CSV: {e}")
            return None
    
    def generate_and_export_daily_report(self, date: str = None, export_csv: bool = True) -> Dict[str, str]:
        """Generate daily report and export to files"""
        report = self.generate_daily_report(date)
        
        results = {}
        
        # Export JSON
        json_file = self.export_to_json(report)
        if json_file:
            results['json'] = json_file
        
        # Export CSV files if requested
        if export_csv:
            if report.get('top_tracks'):
                csv_file = self.export_top_tracks_to_csv(report['top_tracks'])
                if csv_file:
                    results['top_tracks_csv'] = csv_file
            
            if report.get('top_artists'):
                csv_file = self.export_top_artists_to_csv(report['top_artists'])
                if csv_file:
                    results['top_artists_csv'] = csv_file
        
        return results
