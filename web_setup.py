#!/usr/bin/env python3
"""
Spotify Tracker Web Setup Interface
A web-based setup tool for easy configuration and deployment
"""

import os
import sys
import json
import subprocess
import threading
import time
import socket
from pathlib import Path
from flask import Flask, render_template_string, request, jsonify, redirect, url_for
import requests
import urllib.parse

app = Flask(__name__)

# Global state
setup_state = {
    'step': 1,
    'spotify_client_id': '',
    'spotify_client_secret': '',
    'auth_code': '',
    'tokens': None,
    'installation_complete': False,
    'service_running': False,
    'server_ip': None,
    'redirect_uri': None
}

def get_server_ip():
    """Get the server's IP address"""
    try:
        # Try to get the IP address by connecting to a remote server
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        ip = s.getsockname()[0]
        s.close()
        return ip
    except:
        # Fallback methods
        try:
            import subprocess
            result = subprocess.run(['hostname', '-I'], capture_output=True, text=True)
            return result.stdout.strip().split()[0]
        except:
            return '127.0.0.1'

# Initialize server IP and redirect URI
setup_state['server_ip'] = get_server_ip()
setup_state['redirect_uri'] = f"http://{setup_state['server_ip']}:8888/callback"

# HTML Templates
MAIN_TEMPLATE = '''
<!DOCTYPE html>
<html>
<head>
    <title>Spotify Tracker Setup</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; background: #f5f5f5; }
        .container { background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; color: #1DB954; margin-bottom: 30px; }
        .step { background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0; }
        .step.active { border-left: 4px solid #1DB954; }
        .step.completed { background: #d4edda; border-left: 4px solid #28a745; }
        .form-group { margin: 15px 0; }
        .form-group label { display: block; margin-bottom: 5px; font-weight: bold; }
        .form-group input, .form-group textarea { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; box-sizing: border-box; }
        .btn { background: #1DB954; color: white; padding: 12px 24px; border: none; border-radius: 4px; cursor: pointer; font-size: 16px; }
        .btn:hover { background: #1ed760; }
        .btn:disabled { background: #ccc; cursor: not-allowed; }
        .btn-secondary { background: #6c757d; }
        .btn-secondary:hover { background: #545b62; }
        .alert { padding: 15px; margin: 15px 0; border-radius: 4px; }
        .alert-success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .alert-error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .alert-info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .progress { background: #e9ecef; border-radius: 4px; height: 20px; margin: 20px 0; }
        .progress-bar { background: #1DB954; height: 100%; border-radius: 4px; transition: width 0.3s; }
        .code-block { background: #f8f9fa; padding: 15px; border-radius: 4px; font-family: monospace; margin: 10px 0; }
        .status-indicator { display: inline-block; width: 12px; height: 12px; border-radius: 50%; margin-right: 8px; }
        .status-success { background: #28a745; }
        .status-error { background: #dc3545; }
        .status-pending { background: #ffc107; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎵 Spotify Tracker Setup</h1>
            <p>Easy web-based setup for your Ubuntu server</p>
        </div>
        
        <div class="progress">
            <div class="progress-bar" style="width: {{ (step * 20) }}%"></div>
        </div>
        
        {% if step == 1 %}
        <div class="step active">
            <h2>Step 1: Spotify App Configuration</h2>
            <p>First, we need to create a Spotify Developer App:</p>
            <ol>
                <li>Go to <a href="https://developer.spotify.com/dashboard" target="_blank">Spotify Developer Dashboard</a></li>
                <li>Click "Create app"</li>
                <li>Fill in the form:
                    <ul>
                        <li><strong>App name:</strong> My Spotify Tracker</li>
                        <li><strong>App description:</strong> Personal Spotify listening tracker</li>
                        <li><strong>Website:</strong> http://localhost</li>
                        <li><strong>Redirect URI:</strong> <code>{{ redirect_uri }}</code></li>
                        <li><strong>API/SDKs:</strong> Check "Web API"</li>
                    </ul>
                </li>
                <li>Click "Save"</li>
                <li>Copy your Client ID and Client Secret below</li>
            </ol>
            
            <form method="POST" action="/step1">
                <div class="form-group">
                    <label>Spotify Client ID:</label>
                    <input type="text" name="client_id" value="{{ spotify_client_id }}" required>
                </div>
                <div class="form-group">
                    <label>Spotify Client Secret:</label>
                    <input type="text" name="client_secret" value="{{ spotify_client_secret }}" required>
                </div>
                <button type="submit" class="btn">Next Step</button>
            </form>
        </div>
        {% endif %}
        
        {% if step == 2 %}
        <div class="step completed">
            <h2>✅ Step 1: Spotify App - Complete</h2>
        </div>
        <div class="step active">
            <h2>Step 2: Install Dependencies</h2>
            <p>Installing Python packages and setting up the environment...</p>
            <div id="install-status">
                <button onclick="startInstallation()" class="btn">Start Installation</button>
            </div>
        </div>
        {% endif %}
        
        {% if step == 3 %}
        <div class="step completed">
            <h2>✅ Step 1: Spotify App - Complete</h2>
        </div>
        <div class="step completed">
            <h2>✅ Step 2: Installation - Complete</h2>
        </div>
        <div class="step active">
            <h2>Step 3: Authenticate with Spotify</h2>
            <p>Now we need to authorize the app to access your Spotify account:</p>
            <div class="alert alert-info">
                <strong>Ready to authenticate!</strong> Click the button below to start the authorization process.
            </div>
            <button onclick="startAuth()" class="btn">Authorize with Spotify</button>
            <div id="auth-status" style="margin-top: 20px;"></div>
        </div>
        {% endif %}
        
        {% if step == 4 %}
        <div class="step completed">
            <h2>✅ Step 1: Spotify App - Complete</h2>
        </div>
        <div class="step completed">
            <h2>✅ Step 2: Installation - Complete</h2>
        </div>
        <div class="step completed">
            <h2>✅ Step 3: Authentication - Complete</h2>
        </div>
        <div class="step active">
            <h2>Step 4: Start Service</h2>
            <p>Everything is configured! Let's start the Spotify Tracker service:</p>
            <button onclick="startService()" class="btn">Start Spotify Tracker</button>
            <div id="service-status" style="margin-top: 20px;"></div>
        </div>
        {% endif %}
        
        {% if step == 5 %}
        <div class="step completed">
            <h2>✅ Setup Complete!</h2>
            <div class="alert alert-success">
                <strong>Congratulations!</strong> Your Spotify Tracker is now running and collecting data.
            </div>
            
            <h3>Service Status</h3>
            <div id="final-status">
                <span class="status-indicator status-success"></span>Spotify Tracker Service: Running
            </div>
            
            <h3>What's Next?</h3>
            <ul>
                <li>The service will automatically collect your listening data every 10 minutes</li>
                <li>Daily reports will be generated at 1:00 AM</li>
                <li>You can check statistics and generate reports using the command line</li>
            </ul>
            
            <h3>Useful Commands</h3>
            <div class="code-block">
# Check service status<br>
sudo systemctl status spotify-tracker<br><br>
# View logs<br>
sudo journalctl -u spotify-tracker -f<br><br>
# Generate reports<br>
sudo -u spotify-tracker /opt/spotify-tracker/venv/bin/python /opt/spotify-tracker/main.py --report daily<br><br>
# View statistics<br>
sudo -u spotify-tracker /opt/spotify-tracker/venv/bin/python /opt/spotify-tracker/main.py --stats
            </div>
            
            <button onclick="window.location.reload()" class="btn btn-secondary">Refresh Status</button>
        </div>
        {% endif %}
    </div>
    
    <script>
        function startInstallation() {
            document.getElementById('install-status').innerHTML = '<div class="alert alert-info">Installing... Please wait.</div>';
            
            fetch('/install', {method: 'POST'})
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        document.getElementById('install-status').innerHTML = '<div class="alert alert-success">Installation complete!</div>';
                        setTimeout(() => window.location.reload(), 2000);
                    } else {
                        document.getElementById('install-status').innerHTML = '<div class="alert alert-error">Installation failed: ' + data.error + '</div>';
                    }
                });
        }
        
        function startAuth() {
            window.open('/auth', 'spotify_auth', 'width=600,height=700');
            document.getElementById('auth-status').innerHTML = '<div class="alert alert-info">Authorization window opened. Please complete the process in the popup window.</div>';
            
            // Poll for auth completion
            const checkAuth = setInterval(() => {
                fetch('/auth_status')
                    .then(response => response.json())
                    .then(data => {
                        if (data.completed) {
                            clearInterval(checkAuth);
                            document.getElementById('auth-status').innerHTML = '<div class="alert alert-success">Authentication successful!</div>';
                            setTimeout(() => window.location.reload(), 2000);
                        }
                    });
            }, 2000);
        }
        
        function startService() {
            document.getElementById('service-status').innerHTML = '<div class="alert alert-info">Starting service... Please wait.</div>';
            
            fetch('/start_service', {method: 'POST'})
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        document.getElementById('service-status').innerHTML = '<div class="alert alert-success">Service started successfully!</div>';
                        setTimeout(() => window.location.reload(), 2000);
                    } else {
                        document.getElementById('service-status').innerHTML = '<div class="alert alert-error">Failed to start service: ' + data.error + '</div>';
                    }
                });
        }
    </script>
</body>
</html>
'''

@app.route('/')
def index():
    return render_template_string(MAIN_TEMPLATE, **setup_state)

@app.route('/step1', methods=['POST'])
def step1():
    setup_state['spotify_client_id'] = request.form['client_id']
    setup_state['spotify_client_secret'] = request.form['client_secret']
    setup_state['step'] = 2

    # Create .env file
    env_content = f"""SPOTIFY_CLIENT_ID={setup_state['spotify_client_id']}
SPOTIFY_CLIENT_SECRET={setup_state['spotify_client_secret']}
SPOTIFY_REDIRECT_URI={setup_state['redirect_uri']}
DATABASE_PATH=spotify_tracker.db
FETCH_INTERVAL_MINUTES=10
MAX_RECENTLY_PLAYED=50
LOG_LEVEL=INFO
LOG_FILE=spotify_tracker.log
"""

    with open('.env', 'w') as f:
        f.write(env_content)

    return redirect('/')

@app.route('/install', methods=['POST'])
def install():
    try:
        # Run installation commands
        commands = [
            'sudo apt update -qq',
            'sudo apt install -y python3 python3-pip python3-venv python3-dev build-essential sqlite3',
            'python3 -m venv venv',
            './venv/bin/pip install --upgrade pip',
            './venv/bin/pip install flask requests python-dotenv schedule',
        ]

        for cmd in commands:
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
            if result.returncode != 0:
                return jsonify({'success': False, 'error': f'Command failed: {cmd}'})

        setup_state['step'] = 3
        setup_state['installation_complete'] = True
        return jsonify({'success': True})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/auth')
def auth():
    # Generate Spotify auth URL
    client_id = setup_state['spotify_client_id']
    redirect_uri = setup_state['redirect_uri']
    scopes = 'user-read-recently-played user-top-read user-read-playback-state user-read-currently-playing'

    # URL encode the redirect URI
    encoded_redirect_uri = urllib.parse.quote(redirect_uri, safe='')

    auth_url = f"https://accounts.spotify.com/authorize?client_id={client_id}&response_type=code&redirect_uri={encoded_redirect_uri}&scope={scopes}&show_dialog=true"

    return redirect(auth_url)

@app.route('/callback')
def callback():
    code = request.args.get('code')
    error = request.args.get('error')

    if error:
        return f"<h1>Authorization Error</h1><p>{error}</p><script>window.close();</script>"

    if code:
        # Exchange code for tokens
        try:
            import base64

            client_id = setup_state['spotify_client_id']
            client_secret = setup_state['spotify_client_secret']

            auth_header = base64.b64encode(f"{client_id}:{client_secret}".encode()).decode()

            headers = {
                'Authorization': f'Basic {auth_header}',
                'Content-Type': 'application/x-www-form-urlencoded'
            }

            data = {
                'grant_type': 'authorization_code',
                'code': code,
                'redirect_uri': setup_state['redirect_uri']
            }

            response = requests.post('https://accounts.spotify.com/api/token', headers=headers, data=data)

            if response.status_code == 200:
                tokens = response.json()
                setup_state['tokens'] = tokens
                setup_state['step'] = 4

                # Save tokens
                with open('spotify_tokens.json', 'w') as f:
                    json.dump(tokens, f)

                return "<h1>✅ Authentication Successful!</h1><p>You can close this window.</p><script>window.close();</script>"
            else:
                return f"<h1>Token Exchange Failed</h1><p>{response.text}</p><script>window.close();</script>"

        except Exception as e:
            return f"<h1>Error</h1><p>{str(e)}</p><script>window.close();</script>"

    return "<h1>No authorization code received</h1><script>window.close();</script>"

@app.route('/auth_status')
def auth_status():
    return jsonify({'completed': setup_state['step'] >= 4})

@app.route('/start_service', methods=['POST'])
def start_service():
    try:
        # Create a simple main.py for the service
        main_py_content = '''#!/usr/bin/env python3
import json
import time
import requests
import schedule
from datetime import datetime

def fetch_spotify_data():
    print(f"{datetime.now()}: Fetching Spotify data...")
    # This is a simplified version - you can expand this later

def main():
    print("Spotify Tracker started!")
    schedule.every(10).minutes.do(fetch_spotify_data)

    while True:
        schedule.run_pending()
        time.sleep(1)

if __name__ == "__main__":
    main()
'''

        with open('main.py', 'w') as f:
            f.write(main_py_content)

        # Create systemd service
        service_content = f"""[Unit]
Description=Spotify Tracker Service
After=network.target

[Service]
Type=simple
User={os.getenv('USER', 'ubuntu')}
WorkingDirectory={os.getcwd()}
ExecStart={os.getcwd()}/venv/bin/python {os.getcwd()}/main.py
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
"""

        with open('spotify-tracker.service', 'w') as f:
            f.write(service_content)

        # Install and start service
        commands = [
            'sudo cp spotify-tracker.service /etc/systemd/system/',
            'sudo systemctl daemon-reload',
            'sudo systemctl enable spotify-tracker',
            'sudo systemctl start spotify-tracker'
        ]

        for cmd in commands:
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
            if result.returncode != 0:
                return jsonify({'success': False, 'error': f'Command failed: {cmd} - {result.stderr}'})

        setup_state['step'] = 5
        setup_state['service_running'] = True
        return jsonify({'success': True})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

if __name__ == '__main__':
    print("🎵 Spotify Tracker Web Setup")
    print("=" * 40)
    print("Starting web setup interface...")
    print("Open your browser and go to: http://*************:1111")
    print("Press Ctrl+C to stop")

    app.run(host='0.0.0.0', port=8888, debug=False)
