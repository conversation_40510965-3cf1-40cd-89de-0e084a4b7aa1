#!/bin/bash

# Spotify Tracker Quick Setup Script
# This script sets up the web-based installer

echo "🎵 Spotify Tracker - Quick Setup"
echo "================================="

# Check if running as regular user (not root)
if [[ $EUID -eq 0 ]]; then
   echo "❌ Please run this script as a regular user (not with sudo)"
   exit 1
fi

# Update system
echo "📦 Updating system packages..."
sudo apt update

# Install Python and pip
echo "🐍 Installing Python and dependencies..."
sudo apt install -y python3 python3-pip python3-venv

# Install Flask for the web interface
echo "🌐 Installing Flask..."
pip3 install flask requests

# Create directory and set permissions
echo "📁 Setting up directory..."
mkdir -p ~/spotify-tracker
cd ~/spotify-tracker

# Download the web setup file (you'll need to copy this manually)
echo "📋 Please copy the web_setup.py file to this directory:"
echo "   ~/spotify-tracker/web_setup.py"
echo ""
echo "Then run:"
echo "   cd ~/spotify-tracker"
echo "   python3 web_setup.py"
echo ""
echo "And open your browser to: http://*************:1111"
echo ""
echo "✅ Quick setup complete!"
