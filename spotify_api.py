import logging
import time
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any
import requests
from config import Config
from spotify_auth import SpotifyAuth
from database import SpotifyDatabase

class SpotifyAPI:
    def __init__(self):
        self.auth = SpotifyAuth()
        self.db = SpotifyDatabase()
        self.logger = logging.getLogger(__name__)
        self.base_url = Config.SPOTIFY_BASE_URL
    
    def _make_request(self, endpoint: str, params: Dict[str, Any] = None) -> Optional[Dict[str, Any]]:
        """Make authenticated request to Spotify API"""
        access_token = self.auth.get_valid_access_token()
        if not access_token:
            self.logger.error("No valid access token available")
            return None
        
        headers = {
            'Authorization': f'Bearer {access_token}',
            'Content-Type': 'application/json'
        }
        
        url = f"{self.base_url}/{endpoint.lstrip('/')}"
        
        try:
            response = requests.get(url, headers=headers, params=params)
            
            if response.status_code == 401:
                self.logger.warning("Access token expired, attempting refresh")
                # Try to refresh token and retry once
                access_token = self.auth.get_valid_access_token()
                if access_token:
                    headers['Authorization'] = f'Bearer {access_token}'
                    response = requests.get(url, headers=headers, params=params)
                else:
                    self.logger.error("Unable to refresh access token")
                    return None
            
            if response.status_code == 429:
                # Rate limited
                retry_after = int(response.headers.get('Retry-After', 1))
                self.logger.warning(f"Rate limited. Waiting {retry_after} seconds")
                time.sleep(retry_after)
                return self._make_request(endpoint, params)
            
            response.raise_for_status()
            return response.json()
            
        except requests.exceptions.RequestException as e:
            self.logger.error(f"API request failed: {e}")
            return None
    
    def get_recently_played(self, limit: int = 50, after: int = None, before: int = None) -> Optional[List[Dict[str, Any]]]:
        """Get recently played tracks"""
        params = {'limit': min(limit, 50)}  # Spotify API limit is 50
        
        if after:
            params['after'] = after
        if before:
            params['before'] = before
        
        response = self._make_request('me/player/recently-played', params)
        
        if response and 'items' in response:
            self.logger.info(f"Retrieved {len(response['items'])} recently played tracks")
            return response['items']
        
        return []
    
    def get_top_tracks(self, time_range: str = 'medium_term', limit: int = 50) -> Optional[List[Dict[str, Any]]]:
        """Get user's top tracks for a specific time range"""
        valid_ranges = ['short_term', 'medium_term', 'long_term']
        if time_range not in valid_ranges:
            self.logger.error(f"Invalid time range: {time_range}")
            return None
        
        params = {
            'time_range': time_range,
            'limit': min(limit, 50)
        }
        
        response = self._make_request('me/top/tracks', params)
        
        if response and 'items' in response:
            self.logger.info(f"Retrieved {len(response['items'])} top tracks for {time_range}")
            return response['items']
        
        return []
    
    def get_top_artists(self, time_range: str = 'medium_term', limit: int = 50) -> Optional[List[Dict[str, Any]]]:
        """Get user's top artists for a specific time range"""
        valid_ranges = ['short_term', 'medium_term', 'long_term']
        if time_range not in valid_ranges:
            self.logger.error(f"Invalid time range: {time_range}")
            return None
        
        params = {
            'time_range': time_range,
            'limit': min(limit, 50)
        }
        
        response = self._make_request('me/top/artists', params)
        
        if response and 'items' in response:
            self.logger.info(f"Retrieved {len(response['items'])} top artists for {time_range}")
            return response['items']
        
        return []
    
    def get_current_user_profile(self) -> Optional[Dict[str, Any]]:
        """Get current user's profile information"""
        response = self._make_request('me')
        
        if response:
            self.logger.info(f"Retrieved profile for user: {response.get('display_name', 'Unknown')}")
        
        return response
    
    def store_recently_played(self, items: List[Dict[str, Any]]) -> int:
        """Store recently played tracks in database"""
        stored_count = 0
        
        for item in items:
            track = item.get('track')
            played_at = item.get('played_at')
            context = item.get('context')
            
            if not track or not played_at:
                continue
            
            # Store artist data
            for artist in track.get('artists', []):
                self.db.insert_artist(artist)
            
            # Store album data
            if track.get('album'):
                album = track['album']
                # Store album artists
                for artist in album.get('artists', []):
                    self.db.insert_artist(artist)
                self.db.insert_album(album)
            
            # Store track data
            self.db.insert_track(track)
            
            # Store listening session
            if self.db.insert_listening_session(track['id'], played_at, context):
                stored_count += 1
        
        self.logger.info(f"Stored {stored_count} new listening sessions")
        return stored_count
    
    def store_top_tracks(self, tracks: List[Dict[str, Any]], time_range: str) -> bool:
        """Store top tracks in database"""
        if not tracks:
            return False
        
        # Store track and artist data
        for track in tracks:
            # Store artist data
            for artist in track.get('artists', []):
                self.db.insert_artist(artist)
            
            # Store album data
            if track.get('album'):
                album = track['album']
                for artist in album.get('artists', []):
                    self.db.insert_artist(artist)
                self.db.insert_album(album)
            
            # Store track data
            self.db.insert_track(track)
        
        # Store top tracks ranking
        success = self.db.insert_top_tracks(tracks, time_range)
        
        if success:
            self.logger.info(f"Stored {len(tracks)} top tracks for {time_range}")
        
        return success
    
    def store_top_artists(self, artists: List[Dict[str, Any]], time_range: str) -> bool:
        """Store top artists in database"""
        if not artists:
            return False
        
        # Store artist data
        for artist in artists:
            self.db.insert_artist(artist)
        
        # Store top artists ranking
        success = self.db.insert_top_artists(artists, time_range)
        
        if success:
            self.logger.info(f"Stored {len(artists)} top artists for {time_range}")
        
        return success
    
    def fetch_and_store_recently_played(self) -> int:
        """Fetch recently played tracks and store them"""
        recently_played = self.get_recently_played(limit=Config.MAX_RECENTLY_PLAYED)
        
        if recently_played:
            return self.store_recently_played(recently_played)
        
        return 0
    
    def fetch_and_store_top_data(self) -> Dict[str, bool]:
        """Fetch and store top tracks and artists for all time ranges"""
        results = {}
        time_ranges = ['short_term', 'medium_term', 'long_term']
        
        for time_range in time_ranges:
            # Fetch and store top tracks
            top_tracks = self.get_top_tracks(time_range)
            results[f'top_tracks_{time_range}'] = self.store_top_tracks(top_tracks, time_range) if top_tracks else False
            
            # Fetch and store top artists
            top_artists = self.get_top_artists(time_range)
            results[f'top_artists_{time_range}'] = self.store_top_artists(top_artists, time_range) if top_artists else False
            
            # Small delay to avoid rate limiting
            time.sleep(0.1)
        
        return results
