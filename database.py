import sqlite3
import logging
from datetime import datetime
from typing import Optional, List, Dict, Any
from config import Config

class SpotifyDatabase:
    def __init__(self, db_path: str = None):
        self.db_path = db_path or Config.DATABASE_PATH
        self.logger = logging.getLogger(__name__)
        self.init_database()
    
    def get_connection(self):
        """Get database connection with row factory"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        return conn
    
    def init_database(self):
        """Initialize database with all required tables"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            # Artists table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS artists (
                    id TEXT PRIMARY KEY,
                    name TEXT NOT NULL,
                    genres TEXT,
                    popularity INTEGER,
                    followers INTEGER,
                    external_urls TEXT,
                    images TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Albums table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS albums (
                    id TEXT PRIMARY KEY,
                    name TEXT NOT NULL,
                    album_type TEXT,
                    release_date TEXT,
                    total_tracks INTEGER,
                    external_urls TEXT,
                    images TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Tracks table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS tracks (
                    id TEXT PRIMARY KEY,
                    name TEXT NOT NULL,
                    album_id TEXT,
                    duration_ms INTEGER,
                    explicit BOOLEAN,
                    popularity INTEGER,
                    preview_url TEXT,
                    track_number INTEGER,
                    external_urls TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (album_id) REFERENCES albums (id)
                )
            ''')
            
            # Track artists relationship table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS track_artists (
                    track_id TEXT,
                    artist_id TEXT,
                    PRIMARY KEY (track_id, artist_id),
                    FOREIGN KEY (track_id) REFERENCES tracks (id),
                    FOREIGN KEY (artist_id) REFERENCES artists (id)
                )
            ''')
            
            # Album artists relationship table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS album_artists (
                    album_id TEXT,
                    artist_id TEXT,
                    PRIMARY KEY (album_id, artist_id),
                    FOREIGN KEY (album_id) REFERENCES albums (id),
                    FOREIGN KEY (artist_id) REFERENCES artists (id)
                )
            ''')
            
            # Listening sessions table (recently played tracks)
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS listening_sessions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    track_id TEXT NOT NULL,
                    played_at TIMESTAMP NOT NULL,
                    context_type TEXT,
                    context_uri TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (track_id) REFERENCES tracks (id),
                    UNIQUE(track_id, played_at)
                )
            ''')
            
            # Top tracks table (for different time ranges)
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS top_tracks (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    track_id TEXT NOT NULL,
                    time_range TEXT NOT NULL,
                    rank_position INTEGER NOT NULL,
                    recorded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (track_id) REFERENCES tracks (id)
                )
            ''')
            
            # Top artists table (for different time ranges)
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS top_artists (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    artist_id TEXT NOT NULL,
                    time_range TEXT NOT NULL,
                    rank_position INTEGER NOT NULL,
                    recorded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (artist_id) REFERENCES artists (id)
                )
            ''')
            
            # User statistics table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS user_statistics (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    date DATE NOT NULL UNIQUE,
                    total_tracks_played INTEGER DEFAULT 0,
                    total_listening_time_ms INTEGER DEFAULT 0,
                    unique_tracks INTEGER DEFAULT 0,
                    unique_artists INTEGER DEFAULT 0,
                    unique_albums INTEGER DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Create indexes for better performance
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_listening_sessions_played_at ON listening_sessions(played_at)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_listening_sessions_track_id ON listening_sessions(track_id)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_top_tracks_time_range ON top_tracks(time_range, recorded_at)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_top_artists_time_range ON top_artists(time_range, recorded_at)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_user_statistics_date ON user_statistics(date)')
            
            conn.commit()
            self.logger.info("Database initialized successfully")
    
    def insert_artist(self, artist_data: Dict[str, Any]) -> bool:
        """Insert or update artist data"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT OR REPLACE INTO artists 
                    (id, name, genres, popularity, followers, external_urls, images, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
                ''', (
                    artist_data['id'],
                    artist_data['name'],
                    ','.join(artist_data.get('genres', [])),
                    artist_data.get('popularity'),
                    artist_data.get('followers', {}).get('total'),
                    str(artist_data.get('external_urls', {})),
                    str(artist_data.get('images', []))
                ))
                conn.commit()
                return True
        except Exception as e:
            self.logger.error(f"Error inserting artist: {e}")
            return False
    
    def insert_album(self, album_data: Dict[str, Any]) -> bool:
        """Insert album data"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT OR IGNORE INTO albums 
                    (id, name, album_type, release_date, total_tracks, external_urls, images)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (
                    album_data['id'],
                    album_data['name'],
                    album_data.get('album_type'),
                    album_data.get('release_date'),
                    album_data.get('total_tracks'),
                    str(album_data.get('external_urls', {})),
                    str(album_data.get('images', []))
                ))
                
                # Insert album artists
                for artist in album_data.get('artists', []):
                    cursor.execute('''
                        INSERT OR IGNORE INTO album_artists (album_id, artist_id)
                        VALUES (?, ?)
                    ''', (album_data['id'], artist['id']))
                
                conn.commit()
                return True
        except Exception as e:
            self.logger.error(f"Error inserting album: {e}")
            return False

    def insert_track(self, track_data: Dict[str, Any]) -> bool:
        """Insert track data"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT OR IGNORE INTO tracks
                    (id, name, album_id, duration_ms, explicit, popularity, preview_url, track_number, external_urls)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    track_data['id'],
                    track_data['name'],
                    track_data.get('album', {}).get('id'),
                    track_data.get('duration_ms'),
                    track_data.get('explicit'),
                    track_data.get('popularity'),
                    track_data.get('preview_url'),
                    track_data.get('track_number'),
                    str(track_data.get('external_urls', {}))
                ))

                # Insert track artists
                for artist in track_data.get('artists', []):
                    cursor.execute('''
                        INSERT OR IGNORE INTO track_artists (track_id, artist_id)
                        VALUES (?, ?)
                    ''', (track_data['id'], artist['id']))

                conn.commit()
                return True
        except Exception as e:
            self.logger.error(f"Error inserting track: {e}")
            return False

    def insert_listening_session(self, track_id: str, played_at: str, context: Dict[str, Any] = None) -> bool:
        """Insert a listening session"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT OR IGNORE INTO listening_sessions
                    (track_id, played_at, context_type, context_uri)
                    VALUES (?, ?, ?, ?)
                ''', (
                    track_id,
                    played_at,
                    context.get('type') if context else None,
                    context.get('uri') if context else None
                ))
                conn.commit()
                return cursor.rowcount > 0
        except Exception as e:
            self.logger.error(f"Error inserting listening session: {e}")
            return False

    def insert_top_tracks(self, tracks: List[Dict[str, Any]], time_range: str) -> bool:
        """Insert top tracks for a specific time range"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                # Clear existing top tracks for this time range from today
                cursor.execute('''
                    DELETE FROM top_tracks
                    WHERE time_range = ? AND DATE(recorded_at) = DATE('now')
                ''', (time_range,))

                # Insert new top tracks
                for rank, track in enumerate(tracks, 1):
                    cursor.execute('''
                        INSERT INTO top_tracks (track_id, time_range, rank_position)
                        VALUES (?, ?, ?)
                    ''', (track['id'], time_range, rank))

                conn.commit()
                return True
        except Exception as e:
            self.logger.error(f"Error inserting top tracks: {e}")
            return False

    def insert_top_artists(self, artists: List[Dict[str, Any]], time_range: str) -> bool:
        """Insert top artists for a specific time range"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                # Clear existing top artists for this time range from today
                cursor.execute('''
                    DELETE FROM top_artists
                    WHERE time_range = ? AND DATE(recorded_at) = DATE('now')
                ''', (time_range,))

                # Insert new top artists
                for rank, artist in enumerate(artists, 1):
                    cursor.execute('''
                        INSERT INTO top_artists (artist_id, time_range, rank_position)
                        VALUES (?, ?, ?)
                    ''', (artist['id'], time_range, rank))

                conn.commit()
                return True
        except Exception as e:
            self.logger.error(f"Error inserting top artists: {e}")
            return False

    def update_daily_statistics(self, date: str = None) -> bool:
        """Update daily statistics for a specific date"""
        if not date:
            date = datetime.now().strftime('%Y-%m-%d')

        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                # Calculate statistics for the date
                cursor.execute('''
                    SELECT
                        COUNT(*) as total_tracks_played,
                        SUM(t.duration_ms) as total_listening_time_ms,
                        COUNT(DISTINCT ls.track_id) as unique_tracks,
                        COUNT(DISTINCT ta.artist_id) as unique_artists,
                        COUNT(DISTINCT t.album_id) as unique_albums
                    FROM listening_sessions ls
                    JOIN tracks t ON ls.track_id = t.id
                    JOIN track_artists ta ON t.id = ta.track_id
                    WHERE DATE(ls.played_at) = ?
                ''', (date,))

                stats = cursor.fetchone()

                cursor.execute('''
                    INSERT OR REPLACE INTO user_statistics
                    (date, total_tracks_played, total_listening_time_ms, unique_tracks, unique_artists, unique_albums)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (
                    date,
                    stats['total_tracks_played'] or 0,
                    stats['total_listening_time_ms'] or 0,
                    stats['unique_tracks'] or 0,
                    stats['unique_artists'] or 0,
                    stats['unique_albums'] or 0
                ))

                conn.commit()
                return True
        except Exception as e:
            self.logger.error(f"Error updating daily statistics: {e}")
            return False
