import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from database import SpotifyDatabase

class SpotifyStatistics:
    def __init__(self, db: SpotifyDatabase = None):
        self.db = db or SpotifyDatabase()
        self.logger = logging.getLogger(__name__)
    
    def get_listening_time_stats(self, start_date: str = None, end_date: str = None) -> Dict[str, Any]:
        """Get listening time statistics for a date range"""
        try:
            with self.db.get_connection() as conn:
                cursor = conn.cursor()
                
                where_clause = ""
                params = []
                
                if start_date and end_date:
                    where_clause = "WHERE DATE(ls.played_at) BETWEEN ? AND ?"
                    params = [start_date, end_date]
                elif start_date:
                    where_clause = "WHERE DATE(ls.played_at) >= ?"
                    params = [start_date]
                elif end_date:
                    where_clause = "WHERE DATE(ls.played_at) <= ?"
                    params = [end_date]
                
                query = f'''
                    SELECT 
                        COUNT(*) as total_plays,
                        SUM(t.duration_ms) as total_ms,
                        AVG(t.duration_ms) as avg_track_duration_ms,
                        COUNT(DISTINCT ls.track_id) as unique_tracks,
                        COUNT(DISTINCT DATE(ls.played_at)) as active_days
                    FROM listening_sessions ls
                    JOIN tracks t ON ls.track_id = t.id
                    {where_clause}
                '''
                
                cursor.execute(query, params)
                result = cursor.fetchone()
                
                if result:
                    total_ms = result['total_ms'] or 0
                    return {
                        'total_plays': result['total_plays'],
                        'total_listening_time_ms': total_ms,
                        'total_listening_time_minutes': round(total_ms / 60000, 2),
                        'total_listening_time_hours': round(total_ms / 3600000, 2),
                        'average_track_duration_ms': result['avg_track_duration_ms'] or 0,
                        'average_track_duration_minutes': round((result['avg_track_duration_ms'] or 0) / 60000, 2),
                        'unique_tracks': result['unique_tracks'],
                        'active_days': result['active_days'],
                        'average_plays_per_day': round(result['total_plays'] / max(result['active_days'], 1), 2)
                    }
                
        except Exception as e:
            self.logger.error(f"Error getting listening time stats: {e}")
        
        return {}
    
    def get_top_tracks_by_plays(self, limit: int = 20, start_date: str = None, end_date: str = None) -> List[Dict[str, Any]]:
        """Get most played tracks by play count"""
        try:
            with self.db.get_connection() as conn:
                cursor = conn.cursor()
                
                where_clause = ""
                params = []
                
                if start_date and end_date:
                    where_clause = "WHERE DATE(ls.played_at) BETWEEN ? AND ?"
                    params = [start_date, end_date]
                elif start_date:
                    where_clause = "WHERE DATE(ls.played_at) >= ?"
                    params = [start_date]
                elif end_date:
                    where_clause = "WHERE DATE(ls.played_at) <= ?"
                    params = [end_date]
                
                params.append(limit)
                
                query = f'''
                    SELECT 
                        t.id,
                        t.name as track_name,
                        GROUP_CONCAT(DISTINCT a.name, ', ') as artists,
                        al.name as album_name,
                        COUNT(*) as play_count,
                        SUM(t.duration_ms) as total_listening_time_ms,
                        t.duration_ms as track_duration_ms,
                        MAX(ls.played_at) as last_played
                    FROM listening_sessions ls
                    JOIN tracks t ON ls.track_id = t.id
                    JOIN track_artists ta ON t.id = ta.track_id
                    JOIN artists a ON ta.artist_id = a.id
                    LEFT JOIN albums al ON t.album_id = al.id
                    {where_clause}
                    GROUP BY t.id, t.name, al.name, t.duration_ms
                    ORDER BY play_count DESC, last_played DESC
                    LIMIT ?
                '''
                
                cursor.execute(query, params)
                results = cursor.fetchall()
                
                tracks = []
                for row in results:
                    tracks.append({
                        'track_id': row['id'],
                        'track_name': row['track_name'],
                        'artists': row['artists'],
                        'album_name': row['album_name'],
                        'play_count': row['play_count'],
                        'total_listening_time_ms': row['total_listening_time_ms'],
                        'total_listening_time_minutes': round(row['total_listening_time_ms'] / 60000, 2),
                        'track_duration_ms': row['track_duration_ms'],
                        'track_duration_minutes': round(row['track_duration_ms'] / 60000, 2),
                        'last_played': row['last_played']
                    })
                
                return tracks
                
        except Exception as e:
            self.logger.error(f"Error getting top tracks by plays: {e}")
        
        return []
    
    def get_top_artists_by_plays(self, limit: int = 20, start_date: str = None, end_date: str = None) -> List[Dict[str, Any]]:
        """Get most played artists by play count"""
        try:
            with self.db.get_connection() as conn:
                cursor = conn.cursor()
                
                where_clause = ""
                params = []
                
                if start_date and end_date:
                    where_clause = "WHERE DATE(ls.played_at) BETWEEN ? AND ?"
                    params = [start_date, end_date]
                elif start_date:
                    where_clause = "WHERE DATE(ls.played_at) >= ?"
                    params = [start_date]
                elif end_date:
                    where_clause = "WHERE DATE(ls.played_at) <= ?"
                    params = [end_date]
                
                params.append(limit)
                
                query = f'''
                    SELECT 
                        a.id,
                        a.name as artist_name,
                        a.genres,
                        a.popularity,
                        COUNT(*) as play_count,
                        COUNT(DISTINCT t.id) as unique_tracks,
                        SUM(t.duration_ms) as total_listening_time_ms,
                        MAX(ls.played_at) as last_played
                    FROM listening_sessions ls
                    JOIN tracks t ON ls.track_id = t.id
                    JOIN track_artists ta ON t.id = ta.track_id
                    JOIN artists a ON ta.artist_id = a.id
                    {where_clause}
                    GROUP BY a.id, a.name, a.genres, a.popularity
                    ORDER BY play_count DESC, last_played DESC
                    LIMIT ?
                '''
                
                cursor.execute(query, params)
                results = cursor.fetchall()
                
                artists = []
                for row in results:
                    artists.append({
                        'artist_id': row['id'],
                        'artist_name': row['artist_name'],
                        'genres': row['genres'].split(',') if row['genres'] else [],
                        'popularity': row['popularity'],
                        'play_count': row['play_count'],
                        'unique_tracks': row['unique_tracks'],
                        'total_listening_time_ms': row['total_listening_time_ms'],
                        'total_listening_time_minutes': round(row['total_listening_time_ms'] / 60000, 2),
                        'total_listening_time_hours': round(row['total_listening_time_ms'] / 3600000, 2),
                        'last_played': row['last_played']
                    })
                
                return artists
                
        except Exception as e:
            self.logger.error(f"Error getting top artists by plays: {e}")
        
        return []
    
    def get_daily_listening_pattern(self, days: int = 30) -> List[Dict[str, Any]]:
        """Get daily listening patterns for the last N days"""
        try:
            with self.db.get_connection() as conn:
                cursor = conn.cursor()
                
                query = '''
                    SELECT 
                        DATE(ls.played_at) as date,
                        COUNT(*) as total_plays,
                        SUM(t.duration_ms) as total_listening_time_ms,
                        COUNT(DISTINCT ls.track_id) as unique_tracks,
                        COUNT(DISTINCT ta.artist_id) as unique_artists,
                        strftime('%w', ls.played_at) as day_of_week,
                        strftime('%H', ls.played_at) as hour
                    FROM listening_sessions ls
                    JOIN tracks t ON ls.track_id = t.id
                    JOIN track_artists ta ON t.id = ta.track_id
                    WHERE DATE(ls.played_at) >= DATE('now', '-' || ? || ' days')
                    GROUP BY DATE(ls.played_at)
                    ORDER BY date DESC
                '''
                
                cursor.execute(query, (days,))
                results = cursor.fetchall()
                
                daily_stats = []
                for row in results:
                    daily_stats.append({
                        'date': row['date'],
                        'total_plays': row['total_plays'],
                        'total_listening_time_ms': row['total_listening_time_ms'],
                        'total_listening_time_minutes': round(row['total_listening_time_ms'] / 60000, 2),
                        'total_listening_time_hours': round(row['total_listening_time_ms'] / 3600000, 2),
                        'unique_tracks': row['unique_tracks'],
                        'unique_artists': row['unique_artists'],
                        'day_of_week': int(row['day_of_week']),  # 0=Sunday, 6=Saturday
                        'day_name': ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'][int(row['day_of_week'])]
                    })
                
                return daily_stats
                
        except Exception as e:
            self.logger.error(f"Error getting daily listening pattern: {e}")
        
        return []
    
    def get_hourly_listening_pattern(self, days: int = 30) -> List[Dict[str, Any]]:
        """Get hourly listening patterns"""
        try:
            with self.db.get_connection() as conn:
                cursor = conn.cursor()
                
                query = '''
                    SELECT 
                        strftime('%H', ls.played_at) as hour,
                        COUNT(*) as total_plays,
                        SUM(t.duration_ms) as total_listening_time_ms,
                        COUNT(DISTINCT DATE(ls.played_at)) as active_days
                    FROM listening_sessions ls
                    JOIN tracks t ON ls.track_id = t.id
                    WHERE DATE(ls.played_at) >= DATE('now', '-' || ? || ' days')
                    GROUP BY strftime('%H', ls.played_at)
                    ORDER BY hour
                '''
                
                cursor.execute(query, (days,))
                results = cursor.fetchall()
                
                hourly_stats = []
                for row in results:
                    hour = int(row['hour'])
                    hourly_stats.append({
                        'hour': hour,
                        'hour_12': f"{hour % 12 or 12}{'AM' if hour < 12 else 'PM'}",
                        'total_plays': row['total_plays'],
                        'total_listening_time_ms': row['total_listening_time_ms'],
                        'total_listening_time_minutes': round(row['total_listening_time_ms'] / 60000, 2),
                        'average_plays_per_day': round(row['total_plays'] / max(row['active_days'], 1), 2),
                        'active_days': row['active_days']
                    })
                
                return hourly_stats
                
        except Exception as e:
            self.logger.error(f"Error getting hourly listening pattern: {e}")
        
        return []

    def get_genre_distribution(self, limit: int = 20, start_date: str = None, end_date: str = None) -> List[Dict[str, Any]]:
        """Get genre distribution based on listening history"""
        try:
            with self.db.get_connection() as conn:
                cursor = conn.cursor()

                where_clause = ""
                params = []

                if start_date and end_date:
                    where_clause = "WHERE DATE(ls.played_at) BETWEEN ? AND ?"
                    params = [start_date, end_date]
                elif start_date:
                    where_clause = "WHERE DATE(ls.played_at) >= ?"
                    params = [start_date]
                elif end_date:
                    where_clause = "WHERE DATE(ls.played_at) <= ?"
                    params = [end_date]

                # Get all listening sessions with artist genres
                query = f'''
                    SELECT
                        a.genres,
                        COUNT(*) as play_count,
                        SUM(t.duration_ms) as total_listening_time_ms
                    FROM listening_sessions ls
                    JOIN tracks t ON ls.track_id = t.id
                    JOIN track_artists ta ON t.id = ta.track_id
                    JOIN artists a ON ta.artist_id = a.id
                    {where_clause}
                    AND a.genres IS NOT NULL AND a.genres != ''
                    GROUP BY a.genres
                '''

                cursor.execute(query, params)
                results = cursor.fetchall()

                # Process genres (they're stored as comma-separated strings)
                genre_stats = {}
                for row in results:
                    genres = row['genres'].split(',') if row['genres'] else []
                    for genre in genres:
                        genre = genre.strip()
                        if genre:
                            if genre not in genre_stats:
                                genre_stats[genre] = {
                                    'genre': genre,
                                    'play_count': 0,
                                    'total_listening_time_ms': 0
                                }
                            genre_stats[genre]['play_count'] += row['play_count']
                            genre_stats[genre]['total_listening_time_ms'] += row['total_listening_time_ms']

                # Convert to list and sort
                genre_list = list(genre_stats.values())
                genre_list.sort(key=lambda x: x['play_count'], reverse=True)

                # Add calculated fields and limit results
                for genre in genre_list[:limit]:
                    genre['total_listening_time_minutes'] = round(genre['total_listening_time_ms'] / 60000, 2)
                    genre['total_listening_time_hours'] = round(genre['total_listening_time_ms'] / 3600000, 2)

                return genre_list[:limit]

        except Exception as e:
            self.logger.error(f"Error getting genre distribution: {e}")

        return []

    def get_comprehensive_stats(self, days: int = 30) -> Dict[str, Any]:
        """Get comprehensive statistics for the last N days"""
        end_date = datetime.now().strftime('%Y-%m-%d')
        start_date = (datetime.now() - timedelta(days=days)).strftime('%Y-%m-%d')

        return {
            'period': {
                'start_date': start_date,
                'end_date': end_date,
                'days': days
            },
            'listening_time': self.get_listening_time_stats(start_date, end_date),
            'top_tracks': self.get_top_tracks_by_plays(20, start_date, end_date),
            'top_artists': self.get_top_artists_by_plays(20, start_date, end_date),
            'daily_pattern': self.get_daily_listening_pattern(days),
            'hourly_pattern': self.get_hourly_listening_pattern(days),
            'genre_distribution': self.get_genre_distribution(15, start_date, end_date)
        }
