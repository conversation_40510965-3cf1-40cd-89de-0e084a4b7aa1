#!/bin/bash

# Spotify Tracker Installation Script for Ubuntu Server
# This script installs and configures the Spotify Tracker service

set -e

# Configuration
SERVICE_NAME="spotify-tracker"
INSTALL_DIR="/opt/spotify-tracker"
SERVICE_USER="spotify-tracker"
PYTHON_VERSION="3.8"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running as root
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "This script must be run as root (use sudo)"
        exit 1
    fi
}

# Check Ubuntu version
check_ubuntu() {
    if ! grep -q "Ubuntu" /etc/os-release; then
        log_warn "This script is designed for Ubuntu. Proceeding anyway..."
    fi
}

# Install system dependencies
install_dependencies() {
    log_info "Installing system dependencies..."
    
    apt-get update
    apt-get install -y \
        python3 \
        python3-pip \
        python3-venv \
        python3-dev \
        build-essential \
        curl \
        git \
        sqlite3 \
        logrotate
    
    log_info "System dependencies installed"
}

# Create service user
create_user() {
    log_info "Creating service user: $SERVICE_USER"
    
    if id "$SERVICE_USER" &>/dev/null; then
        log_warn "User $SERVICE_USER already exists"
    else
        useradd --system --shell /bin/false --home-dir $INSTALL_DIR --create-home $SERVICE_USER
        log_info "User $SERVICE_USER created"
    fi
}

# Create installation directory
create_directories() {
    log_info "Creating installation directory: $INSTALL_DIR"
    
    mkdir -p $INSTALL_DIR
    mkdir -p $INSTALL_DIR/logs
    mkdir -p $INSTALL_DIR/reports
    
    chown -R $SERVICE_USER:$SERVICE_USER $INSTALL_DIR
    chmod 755 $INSTALL_DIR
}

# Copy application files
copy_files() {
    log_info "Copying application files..."
    
    # Copy Python files
    cp *.py $INSTALL_DIR/
    cp requirements.txt $INSTALL_DIR/
    cp .env.example $INSTALL_DIR/
    
    # Set permissions
    chown -R $SERVICE_USER:$SERVICE_USER $INSTALL_DIR
    chmod +x $INSTALL_DIR/main.py
    
    log_info "Application files copied"
}

# Setup Python virtual environment
setup_venv() {
    log_info "Setting up Python virtual environment..."
    
    cd $INSTALL_DIR
    sudo -u $SERVICE_USER python3 -m venv venv
    sudo -u $SERVICE_USER $INSTALL_DIR/venv/bin/pip install --upgrade pip
    sudo -u $SERVICE_USER $INSTALL_DIR/venv/bin/pip install -r requirements.txt
    
    log_info "Virtual environment setup complete"
}

# Install systemd service
install_service() {
    log_info "Installing systemd service..."
    
    cp spotify-tracker.service /etc/systemd/system/
    systemctl daemon-reload
    systemctl enable $SERVICE_NAME
    
    log_info "Systemd service installed and enabled"
}

# Setup logrotate
setup_logrotate() {
    log_info "Setting up log rotation..."
    
    cat > /etc/logrotate.d/spotify-tracker << EOF
$INSTALL_DIR/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 $SERVICE_USER $SERVICE_USER
    postrotate
        systemctl reload $SERVICE_NAME > /dev/null 2>&1 || true
    endscript
}
EOF
    
    log_info "Log rotation configured"
}

# Create configuration file
create_config() {
    log_info "Creating configuration file..."
    
    if [[ ! -f $INSTALL_DIR/.env ]]; then
        cp $INSTALL_DIR/.env.example $INSTALL_DIR/.env
        chown $SERVICE_USER:$SERVICE_USER $INSTALL_DIR/.env
        chmod 600 $INSTALL_DIR/.env
        
        log_warn "Configuration file created at $INSTALL_DIR/.env"
        log_warn "Please edit this file with your Spotify API credentials before starting the service"
    else
        log_info "Configuration file already exists"
    fi
}

# Main installation function
main() {
    log_info "Starting Spotify Tracker installation..."
    
    check_root
    check_ubuntu
    install_dependencies
    create_user
    create_directories
    copy_files
    setup_venv
    install_service
    setup_logrotate
    create_config
    
    log_info "Installation completed successfully!"
    echo
    log_info "Next steps:"
    echo "1. Edit the configuration file: $INSTALL_DIR/.env"
    echo "2. Add your Spotify API credentials (get them from https://developer.spotify.com/dashboard)"
    echo "3. Authenticate with Spotify: sudo -u $SERVICE_USER $INSTALL_DIR/venv/bin/python $INSTALL_DIR/main.py --auth"
    echo "4. Start the service: systemctl start $SERVICE_NAME"
    echo "5. Check service status: systemctl status $SERVICE_NAME"
    echo "6. View logs: journalctl -u $SERVICE_NAME -f"
    echo
    log_info "Service commands:"
    echo "  Start:   systemctl start $SERVICE_NAME"
    echo "  Stop:    systemctl stop $SERVICE_NAME"
    echo "  Restart: systemctl restart $SERVICE_NAME"
    echo "  Status:  systemctl status $SERVICE_NAME"
    echo "  Logs:    journalctl -u $SERVICE_NAME -f"
}

# Run main function
main "$@"
