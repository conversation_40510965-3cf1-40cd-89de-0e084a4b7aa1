#!/usr/bin/env python3
"""
Development setup script for Spotify Tracker
Use this for local development and testing
"""

import os
import sys
import subprocess
from pathlib import Path

def run_command(command, description):
    """Run a command and handle errors"""
    print(f"🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed: {e}")
        if e.stdout:
            print(f"STDOUT: {e.stdout}")
        if e.stderr:
            print(f"STDERR: {e.stderr}")
        return False

def main():
    print("🎵 Spotify Tracker - Development Setup")
    print("=" * 40)
    
    # Check Python version
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher is required")
        sys.exit(1)
    
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor} detected")
    
    # Create virtual environment
    if not Path("venv").exists():
        if not run_command("python -m venv venv", "Creating virtual environment"):
            sys.exit(1)
    else:
        print("✅ Virtual environment already exists")
    
    # Determine pip path based on OS
    if os.name == 'nt':  # Windows
        pip_path = "venv\\Scripts\\pip"
        python_path = "venv\\Scripts\\python"
    else:  # Unix/Linux/macOS
        pip_path = "venv/bin/pip"
        python_path = "venv/bin/python"
    
    # Upgrade pip
    if not run_command(f"{pip_path} install --upgrade pip", "Upgrading pip"):
        sys.exit(1)
    
    # Install requirements
    if not run_command(f"{pip_path} install -r requirements.txt", "Installing requirements"):
        sys.exit(1)
    
    # Create .env file if it doesn't exist
    if not Path(".env").exists():
        if Path(".env.example").exists():
            import shutil
            shutil.copy(".env.example", ".env")
            print("✅ Created .env file from .env.example")
        else:
            print("⚠️  .env.example not found, creating basic .env file")
            with open(".env", "w") as f:
                f.write("# Spotify API Credentials\n")
                f.write("SPOTIFY_CLIENT_ID=your_client_id_here\n")
                f.write("SPOTIFY_CLIENT_SECRET=your_client_secret_here\n")
                f.write("SPOTIFY_REDIRECT_URI=http://localhost:8080/callback\n")
    else:
        print("✅ .env file already exists")
    
    # Create necessary directories
    directories = ["logs", "reports"]
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"✅ Created {directory} directory")
    
    print("\n🎉 Setup completed successfully!")
    print("\n📋 Next steps:")
    print("1. Edit .env file with your Spotify API credentials")
    print("   Get them from: https://developer.spotify.com/dashboard")
    print(f"2. Authenticate: {python_path} main.py --auth")
    print(f"3. Test: {python_path} main.py --once")
    print(f"4. View stats: {python_path} main.py --stats")
    print(f"5. Run daemon: {python_path} main.py --daemon")
    
    print("\n🔧 Development commands:")
    print(f"  Activate venv: {'venv\\\\Scripts\\\\activate' if os.name == 'nt' else 'source venv/bin/activate'}")
    print(f"  Run tests: {python_path} -m pytest (if you add tests)")
    print(f"  Check status: {python_path} main.py --status")

if __name__ == "__main__":
    main()
