#!/usr/bin/env python3
"""
Spotify Tracker - Main Application
Tracks Spotify listening history, generates statistics, and creates reports.
"""

import argparse
import signal
import sys
import time
from datetime import datetime
from pathlib import Path

# Setup logging first
from logging_config import setup_logging, health_monitor
setup_logging()

import logging
from config import Config
from spotify_auth import SpotifyAuth
from spotify_api import SpotifyAPI
from scheduler import SpotifyScheduler
from statistics import SpotifyStatistics
from reports import SpotifyReports
from database import SpotifyDatabase

logger = logging.getLogger(__name__)

class SpotifyTracker:
    def __init__(self):
        self.auth = SpotifyAuth()
        self.api = SpotifyAPI()
        self.scheduler = SpotifyScheduler()
        self.stats = SpotifyStatistics()
        self.reports = SpotifyReports()
        self.db = SpotifyDatabase()
        
        # Setup signal handlers for graceful shutdown
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals gracefully"""
        logger.info(f"Received signal {signum}, shutting down gracefully...")
        self.shutdown()
        sys.exit(0)
    
    def authenticate(self, manual: bool = False) -> bool:
        """Authenticate with Spotify"""
        logger.info("Starting Spotify authentication")

        if self.auth.is_authenticated():
            logger.info("Already authenticated with Spotify")
            return True

        if manual:
            return self.auth.authenticate_interactive()
        else:
            # Try automatic authentication first
            if self.auth.authenticate_with_server():
                return True
            else:
                print("\n" + "="*50)
                print("Automatic authentication failed. Trying manual mode...")
                print("="*50)
                return self.auth.authenticate_interactive()
    
    def run_daemon(self):
        """Run as a daemon with scheduled tasks"""
        logger.info("Starting Spotify Tracker in daemon mode")
        
        if not self.auth.is_authenticated():
            logger.error("Not authenticated. Please run with --auth first.")
            return False
        
        # Start the scheduler
        if not self.scheduler.start():
            logger.error("Failed to start scheduler")
            return False
        
        try:
            # Keep the main thread alive
            while self.scheduler.is_running():
                time.sleep(10)
                
                # Periodically log health status
                if datetime.now().minute % 30 == 0:  # Every 30 minutes
                    health_monitor.log_health_summary()
        
        except KeyboardInterrupt:
            logger.info("Daemon interrupted by user")
        
        finally:
            self.shutdown()
        
        return True
    
    def run_once(self):
        """Run data collection once and exit"""
        logger.info("Running one-time data collection")
        
        if not self.auth.is_authenticated():
            logger.error("Not authenticated. Please run with --auth first.")
            return False
        
        try:
            # Fetch recently played tracks
            logger.info("Fetching recently played tracks...")
            stored_count = self.api.fetch_and_store_recently_played()
            logger.info(f"Stored {stored_count} new listening sessions")
            
            # Fetch top data
            logger.info("Fetching top tracks and artists...")
            results = self.api.fetch_and_store_top_data()
            success_count = sum(1 for success in results.values() if success)
            logger.info(f"Top data fetch completed. Success: {success_count}/{len(results)}")
            
            # Update daily statistics
            today = datetime.now().strftime('%Y-%m-%d')
            self.db.update_daily_statistics(today)
            logger.info("Updated daily statistics")
            
            return True
            
        except Exception as e:
            logger.error(f"Error during one-time run: {e}")
            return False
    
    def generate_report(self, report_type: str = 'daily', date: str = None):
        """Generate and export a report"""
        logger.info(f"Generating {report_type} report")
        
        try:
            if report_type == 'daily':
                results = self.reports.generate_and_export_daily_report(date)
            elif report_type == 'weekly':
                report = self.reports.generate_weekly_report(date)
                results = {'json': self.reports.export_to_json(report)}
            elif report_type == 'monthly':
                if date:
                    year, month = map(int, date.split('-'))
                    report = self.reports.generate_monthly_report(year, month)
                else:
                    report = self.reports.generate_monthly_report()
                results = {'json': self.reports.export_to_json(report)}
            else:
                logger.error(f"Unknown report type: {report_type}")
                return False
            
            if results:
                logger.info(f"Report generated successfully: {results}")
                print(f"Report files created: {results}")
                return True
            else:
                logger.error("Report generation failed")
                return False
                
        except Exception as e:
            logger.error(f"Error generating report: {e}")
            return False
    
    def show_stats(self, days: int = 30):
        """Show current statistics"""
        logger.info(f"Showing statistics for last {days} days")
        
        try:
            stats = self.stats.get_comprehensive_stats(days)
            
            print(f"\n=== SPOTIFY LISTENING STATISTICS (Last {days} days) ===")
            print(f"Period: {stats['period']['start_date']} to {stats['period']['end_date']}")
            
            # Listening time stats
            listening = stats['listening_time']
            print(f"\n📊 LISTENING SUMMARY:")
            print(f"  Total plays: {listening.get('total_plays', 0):,}")
            print(f"  Total listening time: {listening.get('total_listening_time_hours', 0):.1f} hours")
            print(f"  Average per day: {listening.get('average_plays_per_day', 0):.1f} plays")
            print(f"  Unique tracks: {listening.get('unique_tracks', 0):,}")
            print(f"  Active days: {listening.get('active_days', 0)}")
            
            # Top tracks
            print(f"\n🎵 TOP TRACKS:")
            for i, track in enumerate(stats['top_tracks'][:10], 1):
                print(f"  {i:2d}. {track['track_name']} - {track['artists']} ({track['play_count']} plays)")
            
            # Top artists
            print(f"\n🎤 TOP ARTISTS:")
            for i, artist in enumerate(stats['top_artists'][:10], 1):
                print(f"  {i:2d}. {artist['artist_name']} ({artist['play_count']} plays, {artist['total_listening_time_hours']:.1f}h)")
            
            # Top genres
            print(f"\n🎼 TOP GENRES:")
            for i, genre in enumerate(stats['genre_distribution'][:10], 1):
                print(f"  {i:2d}. {genre['genre']} ({genre['play_count']} plays)")
            
            return True
            
        except Exception as e:
            logger.error(f"Error showing statistics: {e}")
            return False
    
    def show_status(self):
        """Show current system status"""
        print("\n=== SPOTIFY TRACKER STATUS ===")
        
        # Authentication status
        auth_status = "✅ Authenticated" if self.auth.is_authenticated() else "❌ Not authenticated"
        print(f"Authentication: {auth_status}")
        
        # Scheduler status
        scheduler_status = self.scheduler.get_status()
        scheduler_running = "✅ Running" if scheduler_status['running'] else "❌ Stopped"
        print(f"Scheduler: {scheduler_running}")
        
        if scheduler_status['running']:
            print(f"  Next run: {scheduler_status['next_run']}")
            print(f"  Consecutive errors: {scheduler_status['consecutive_errors']}")
            print(f"  Last successful fetch: {scheduler_status['last_successful_fetch']}")
        
        # Health status
        health_status = health_monitor.get_health_status()
        print(f"\nHealth Score: {health_status['health_score']}")
        print(f"Uptime: {health_status['uptime_hours']:.1f} hours")
        print(f"API Success Rate: {health_status['api_success_rate']}%")
        print(f"Tracks Stored: {health_status['tracks_stored']:,}")
        
        return True
    
    def shutdown(self):
        """Graceful shutdown"""
        logger.info("Shutting down Spotify Tracker")
        
        if self.scheduler.is_running():
            self.scheduler.stop()
        
        logger.info("Shutdown complete")

def main():
    parser = argparse.ArgumentParser(description='Spotify Tracker - Track your Spotify listening habits')
    parser.add_argument('--auth', action='store_true', help='Authenticate with Spotify')
    parser.add_argument('--manual', action='store_true', help='Use manual authentication (paste URL method)')
    parser.add_argument('--daemon', action='store_true', help='Run as daemon with scheduled tasks')
    parser.add_argument('--once', action='store_true', help='Run data collection once and exit')
    parser.add_argument('--report', choices=['daily', 'weekly', 'monthly'], help='Generate a report')
    parser.add_argument('--date', help='Date for report (YYYY-MM-DD for daily/weekly, YYYY-MM for monthly)')
    parser.add_argument('--stats', type=int, metavar='DAYS', nargs='?', const=30, help='Show statistics for last N days (default: 30)')
    parser.add_argument('--status', action='store_true', help='Show current system status')
    
    args = parser.parse_args()
    
    # Create tracker instance
    tracker = SpotifyTracker()
    
    try:
        if args.auth:
            success = tracker.authenticate(manual=args.manual)
            sys.exit(0 if success else 1)
        
        elif args.daemon:
            success = tracker.run_daemon()
            sys.exit(0 if success else 1)
        
        elif args.once:
            success = tracker.run_once()
            sys.exit(0 if success else 1)
        
        elif args.report:
            success = tracker.generate_report(args.report, args.date)
            sys.exit(0 if success else 1)
        
        elif args.stats is not None:
            success = tracker.show_stats(args.stats)
            sys.exit(0 if success else 1)
        
        elif args.status:
            success = tracker.show_status()
            sys.exit(0 if success else 1)
        
        else:
            parser.print_help()
            sys.exit(1)
    
    except Exception as e:
        logger.error(f"Unhandled error: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
