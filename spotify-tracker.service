[Unit]
Description=Spotify Tracker Service
After=network.target
Wants=network-online.target

[Service]
Type=simple
User=spotify-tracker
Group=spotify-tracker
WorkingDirectory=/opt/spotify-tracker
ExecStart=/opt/spotify-tracker/venv/bin/python /opt/spotify-tracker/main.py --daemon
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal

# Environment variables
Environment=PYTHONPATH=/opt/spotify-tracker
Environment=PYTHONUNBUFFERED=1

# Security settings
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/opt/spotify-tracker

# Resource limits
LimitNOFILE=1024
MemoryMax=512M

[Install]
WantedBy=multi-user.target
