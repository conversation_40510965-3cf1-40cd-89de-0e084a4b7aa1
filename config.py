import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class Config:
    # Spotify API Configuration
    SPOTIFY_CLIENT_ID = os.getenv('SPOTIFY_CLIENT_ID')
    SPOTIFY_CLIENT_SECRET = os.getenv('SPOTIFY_CLIENT_SECRET')
    SPOTIFY_REDIRECT_URI = os.getenv('SPOTIFY_REDIRECT_URI', 'http://localhost:8080/callback')
    
    # Database Configuration
    DATABASE_PATH = os.getenv('DATABASE_PATH', 'spotify_tracker.db')
    
    # Tracking Configuration
    FETCH_INTERVAL_MINUTES = int(os.getenv('FETCH_INTERVAL_MINUTES', 10))
    MAX_RECENTLY_PLAYED = int(os.getenv('MAX_RECENTLY_PLAYED', 50))
    
    # Logging Configuration
    LOG_LEVEL = os.getenv('LOG_LEVEL', 'INFO')
    LOG_FILE = os.getenv('LOG_FILE', 'spotify_tracker.log')
    
    # Spotify API Endpoints
    SPOTIFY_BASE_URL = 'https://api.spotify.com/v1'
    SPOTIFY_AUTH_URL = 'https://accounts.spotify.com/authorize'
    SPOTIFY_TOKEN_URL = 'https://accounts.spotify.com/api/token'
    
    # Required Spotify Scopes
    SPOTIFY_SCOPES = [
        'user-read-recently-played',
        'user-top-read',
        'user-read-playback-state',
        'user-read-currently-playing'
    ]
    
    @classmethod
    def validate(cls):
        """Validate that all required configuration is present"""
        required_vars = [
            'SPOTIFY_CLIENT_ID',
            'SPOTIFY_CLIENT_SECRET'
        ]
        
        missing_vars = []
        for var in required_vars:
            if not getattr(cls, var):
                missing_vars.append(var)
        
        if missing_vars:
            raise ValueError(f"Missing required environment variables: {', '.join(missing_vars)}")
        
        return True
